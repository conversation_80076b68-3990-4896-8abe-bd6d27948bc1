#!/bin/bash
# ERA5下载器安装和配置脚本
# Setup script for ERA5 downloader

echo "🌍 ERA5数据下载器安装脚本"
echo "=================================="

# 检查Python环境
echo "🐍 检查Python环境..."
if ! command -v python &> /dev/null; then
    echo "❌ 错误: 未找到Python"
    echo "请先安装Python 3.7或更高版本"
    exit 1
fi

python_version=$(python --version 2>&1 | cut -d' ' -f2)
echo "✅ Python版本: $python_version"

# 安装依赖
echo ""
echo "📦 安装Python依赖..."
pip install cdsapi

if [ $? -eq 0 ]; then
    echo "✅ cdsapi安装成功"
else
    echo "❌ cdsapi安装失败"
    exit 1
fi

# 检查CDS配置
echo ""
echo "🔑 检查CDS API配置..."
cdsapirc_file="$HOME/.cdsapirc"

if [ -f "$cdsapirc_file" ]; then
    echo "✅ 找到CDS配置文件: $cdsapirc_file"
    
    # 验证配置
    python -c "import cdsapi; c = cdsapi.Client(); print('✅ CDS配置验证成功')" 2>/dev/null
    if [ $? -eq 0 ]; then
        echo "✅ CDS API配置有效"
    else
        echo "⚠️ CDS API配置可能有问题，请检查密钥"
    fi
else
    echo "❌ 未找到CDS配置文件"
    echo ""
    echo "请按以下步骤配置:"
    echo "1. 注册CDS账户: https://cds.climate.copernicus.eu/user/register"
    echo "2. 获取API密钥: https://cds.climate.copernicus.eu/api-how-to"
    echo "3. 创建配置文件:"
    echo "   cp cdsapirc_example.txt ~/.cdsapirc"
    echo "   # 然后编辑 ~/.cdsapirc 文件，替换YOUR_API_KEY_HERE为您的实际密钥"
    echo "4. 设置文件权限:"
    echo "   chmod 600 ~/.cdsapirc"
fi

# 创建必要目录
echo ""
echo "📁 创建数据目录..."
mkdir -p /Volumes/LaCie/data/era5 2>/dev/null || echo "⚠️ 无法创建外部存储目录，将使用本地目录"
mkdir -p data/era5
mkdir -p logs
mkdir -p status

echo "✅ 目录创建完成"

# 测试下载器
echo ""
echo "🧪 测试下载器..."
python era5_downloader.py --help > /dev/null 2>&1

if [ $? -eq 0 ]; then
    echo "✅ ERA5下载器测试通过"
else
    echo "❌ ERA5下载器测试失败"
    exit 1
fi

echo ""
echo "🎉 安装完成!"
echo ""
echo "📖 使用方法:"
echo "  python era5_downloader.py --help     # 查看帮助"
echo "  python era5_downloader.py --check    # 检查现有文件"
echo "  python era5_downloader.py --year 2020 # 下载2020年数据"
echo ""
echo "📚 更多信息请查看 README.md"
