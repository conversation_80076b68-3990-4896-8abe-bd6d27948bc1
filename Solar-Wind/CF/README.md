# 可再生能源容量因子计算模块
## Renewable Energy Capacity Factor Calculator Module

基于Zheng et al. & Wang et al. (Nature Communications, 2025)方法论的完整实现

### 模块概述

本模块实现了严格遵循Nature Communications文献方法论的可再生能源容量因子计算系统，包括：

- **太阳能光伏容量因子计算**: 基于Zheng et al.方法，考虑太阳几何、温度修正、系统效率
- **陆上风电容量因子计算**: 基于GE 2.5MW风机功率曲线和风速外推
- **海上风电容量因子计算**: 基于Vestas 8.0MW风机功率曲线（如有海上适宜性数据）
- **多年数据处理**: 支持多年份数据计算以降低不确定性
- **空间采样策略**: 处理0.5°网格内气象条件的空间异质性

### 文件结构

```
Solar-Wind/CF/
├── capacity_factor_calculator.py  # 主计算脚本
├── era5_downloader.py             # ERA5数据下载工具
├── README.md                      # 本文档
├── data/                          # 数据目录
│   └── era5/                      # ERA5气象数据
├── outputs/                       # 输出结果
│   ├── solar_cf/                  # 太阳能容量因子结果
│   ├── wind_cf/                   # 风电容量因子结果
│   ├── processed_data/            # 处理后的数据
│   ├── visualizations/            # 可视化图表
│   └── reports/                   # 统计报告
└── config/                        # 配置文件
    └── cf_config.py               # 系统配置
```

### 技术参数设置

#### 陆上风电 (基于Zheng et al.)
- **风机型号**: General Electric 2.5 MW
- **轮毂高度**: 100米
- **装机密度**: 2.7 MW/km²
- **功率曲线**: 标准GE 2.5MW功率曲线
- **风速外推**: 幂律公式，指数α=1/7

#### 海上风电 (基于Zheng et al.)
- **风机型号**: Vestas 8.0 MW
- **轮毂高度**: 100米
- **装机密度**: 4.6 MW/km²
- **功率曲线**: 标准Vestas 8.0MW功率曲线

#### 太阳能光伏 (基于Zheng et al.)
- **光电转换效率**: 16.19%
- **系统性能系数**: 80.56%
- **温度系数**: 0.005 °C⁻¹
- **装机密度**: 74 W/m²
- **跟踪系统**: 固定式，最优倾斜角

### 核心计算公式

#### 风电容量因子计算
```
CFw = fw(Vhub)                                    # 公式(1)
Vhub = V10 × (hub/10)^α                          # 公式(2)
V10 = √(uas² + vas²)                             # 公式(3)
```

#### 太阳能容量因子计算
```
CFs = PGHI / PWp                                 # 公式(4)
PGHI = IΣ × EF × TEMcoef × SYScoef              # 公式(5)
IΣ = IBΣ + IDΣ + IRΣ                            # 公式(6)
TEMcoef = 1 - γ × (Tcell - TSTC)                # 公式(13)
Tcell = c1 + c2×T + c3×I - c4×V                 # 公式(14)
```

### 使用方法

#### 1. 环境准备

```bash
# 激活solar-wind虚拟环境
conda activate solar-wind

# 安装必要的Python包
pip install cdsapi xarray netcdf4 rasterio geopandas
```

#### 2. 配置CDS API

创建 `~/.cdsapirc` 文件：
```
url: https://cds.climate.copernicus.eu/api/v2
key: YOUR_API_KEY_HERE
```

获取API密钥：
1. 注册CDS账户: https://cds.climate.copernicus.eu/
2. 登录后在个人资料页面获取API密钥

#### 3. 下载ERA5数据

```bash
cd Solar-Wind/CF
python era5_downloader.py
```

**新的ERA5下载器功能特点：**
- ✅ **智能下载**: 自动检查已存在文件，避免重复下载
- ✅ **断点续传**: 支持分年份/分月份下载，可随时中断和恢复
- ✅ **进度监控**: 实时显示下载进度和预计剩余时间
- ✅ **错误处理**: 自动重试机制，完整的日志记录
- ✅ **多种模式**: 支持交互式和命令行模式

**下载的ERA5变量：**
- **太阳辐射**: surface_solar_radiation_downwards (独立文件)
- **温度**: 2m_temperature (独立文件)
- **风能**: 10m_u_component_of_wind, 10m_v_component_of_wind

**数据规格：**
- 时间范围: 2000-2024年 (25年数据)
- 时间分辨率: 小时级 (8760小时/年)
- 空间分辨率: 0.25° × 0.25°
- 存储位置: `/Volumes/LaCie/data/era5/`

**使用方式：**
```bash
# 交互式运行
python era5_downloader.py

# 下载指定年份（所有数据类型）
python era5_downloader.py --year 2020

# 下载指定年份（仅太阳辐射数据）
python era5_downloader.py --year 2020 --types solar_radiation

# 下载指定年份（太阳辐射和温度数据）
python era5_downloader.py --year 2020 --types solar_radiation temperature

# 下载年份范围
python era5_downloader.py --range 2020 2022

# 检查现有文件
python era5_downloader.py --check

# 生成摘要报告
python era5_downloader.py --summary
```

#### 4. 运行容量因子计算

```bash
python capacity_factor_calculator.py
```

### 输出结果

#### 主要输出文件

1. **capacity_factors_summary_YYYY_YYYY.csv**
   - 网格级容量因子汇总数据
   - 包含多年平均值和标准差

2. **capacity_factors_detailed_YYYY_YYYY.json**
   - 详细的8760小时容量因子数据
   - 按网格和年份组织

3. **capacity_factors_report_YYYY_YYYY.md**
   - 统计分析报告
   - 包含技术参数和计算结果摘要

4. **capacity_factors_visualization.png**
   - 容量因子分布可视化
   - 包含直方图和空间分布图

#### 数据格式示例

```python
# CSV汇总数据格式
{
    'grid_i': 180,                    # 网格行索引
    'grid_j': 360,                    # 网格列索引
    'latitude': 0.25,                 # 纬度
    'longitude': 0.25,                # 经度
    'technology': 'solar',            # 技术类型
    'solar_cf_avg': 0.234,           # 太阳能多年平均容量因子
    'wind_cf_avg': 0.456,            # 风电多年平均容量因子
    'solar_cf_std': 0.012,           # 太阳能容量因子标准差
    'wind_cf_std': 0.023,            # 风电容量因子标准差
    'years_count': 3                  # 计算年份数
}
```

### 质量控制

#### 数据验证
- **容量因子范围检查**: 太阳能0.05-0.35，风电0.10-0.60
- **时间序列完整性**: 检查缺失数据和异常值
- **空间一致性**: 验证相邻网格的合理性
- **物理一致性**: 检查计算结果的物理合理性

#### 不确定性分析
- **多年数据**: 使用多年数据计算标准差
- **参数敏感性**: 评估关键参数变化的影响
- **方法对比**: 与已有研究结果对比验证

### 配置参数

主要配置参数在 `capacity_factor_calculator.py` 中可调整：

```python
# 处理参数
years_list = [2020, 2021, 2022]     # 处理年份
sample_ratio = 1.0                   # 采样比例 (1.0=全部处理)
era5_data_dir = "data/era5"         # ERA5数据目录
output_dir = "outputs"              # 输出目录

# 技术参数 (基于文献设置，一般不需修改)
onshore_wind_params = {...}         # 陆上风电参数
offshore_wind_params = {...}        # 海上风电参数
solar_pv_params = {...}             # 太阳能光伏参数
```

### 性能优化

#### 计算效率
- **采样策略**: 可设置sample_ratio进行快速测试
- **并行处理**: 支持网格级并行计算
- **内存管理**: 分批处理大数据集
- **缓存机制**: 避免重复计算

#### 存储优化
- **数据压缩**: 使用压缩格式减少存储空间
- **分级存储**: 汇总数据和详细数据分别存储
- **格式选择**: CSV用于分析，JSON用于详细数据

### 与现有模块集成

#### 输入数据
- **适宜性掩码**: 来自 `../Suitability/outputs/`
- **ERA5气象数据**: 来自 `data/era5/`

#### 输出兼容性
- **坐标系**: WGS84 (EPSG:4326)
- **分辨率**: 0.5° × 0.5°
- **数据格式**: 与国家级聚合模块兼容

### 故障排除

#### 常见问题

1. **CDS API错误**
   - 检查API密钥配置
   - 确认网络连接
   - 验证CDS账户状态

2. **内存不足**
   - 减少sample_ratio
   - 分年份处理
   - 增加系统内存

3. **数据缺失**
   - 检查ERA5数据完整性
   - 验证适宜性掩码文件
   - 确认文件路径正确

4. **计算结果异常**
   - 检查技术参数设置
   - 验证气象数据质量
   - 对比已知区域结果

#### 日志和调试
- 详细的进度输出
- 错误信息记录
- 中间结果保存
- 统计信息报告

### 文献引用

本模块基于以下文献方法：

```
Zheng, X., et al. (2025). Global renewable energy potential assessment. 
Nature Communications.

Wang, S., et al. (2025). Offshore wind energy development constraints. 
Nature Communications.
```

### 技术支持

如遇问题，请检查：
1. 环境配置是否正确
2. 数据文件是否完整
3. 参数设置是否合理
4. 系统资源是否充足

### 更新日志

- **v1.0** (2025-01): 初始版本，基于Nature Communications方法论
- 严格遵循Zheng et al.和Wang et al.的技术参数和计算公式
- 支持多年数据处理和不确定性分析
- 集成空间采样策略和质量控制机制
