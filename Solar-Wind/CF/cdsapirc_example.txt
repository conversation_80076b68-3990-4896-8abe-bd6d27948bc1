# CDS API配置文件示例
# 请将此文件重命名为 .cdsapirc 并放置在用户主目录下

# 配置步骤:
# 1. 注册CDS账户: https://cds.climate.copernicus.eu/user/register
# 2. 登录后访问: https://cds.climate.copernicus.eu/api-how-to
# 3. 复制您的API密钥
# 4. 将下面的 YOUR_API_KEY_HERE 替换为您的实际API密钥

url: https://cds.climate.copernicus.eu/api/v2
key: YOUR_API_KEY_HERE

# 注意事项:
# - API密钥格式通常为: 12345:abcdef12-3456-7890-abcd-ef1234567890
# - 请确保密钥前后没有多余的空格
# - 此文件应保存为 ~/.cdsapirc (Linux/Mac) 或 %USERPROFILE%\.cdsapirc (Windows)
# - 文件权限应设置为仅用户可读 (chmod 600 ~/.cdsapirc)

# 验证配置:
# 运行以下命令验证配置是否正确:
# python -c "import cdsapi; c = cdsapi.Client(); print('CDS配置成功!')"
