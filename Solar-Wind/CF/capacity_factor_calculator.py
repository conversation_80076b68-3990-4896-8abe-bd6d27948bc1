#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
可再生能源容量因子计算系统
Renewable Energy Capacity Factor Calculator

基于Zheng et al. (Nature Communications, 2025)和<PERSON> et al. (Nature Communications, 2025)方法论
严格遵循文献中的科学计算公式和技术参数
"""

import os
import sys
import numpy as np
import pandas as pd
import xarray as xr
import rasterio
from rasterio.transform import from_bounds
from rasterio.features import rasterize
import geopandas as gpd
from scipy.interpolate import interp1d
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import warnings
from pathlib import Path
import gc
from tqdm import tqdm
warnings.filterwarnings('ignore')

class RenewableEnergyCalculator:
    """
    可再生能源容量因子计算器
    
    基于Nature Communications文献方法论的完整实现
    """
    
    def __init__(self, suitability_data_dir="../Suitability/outputs/final_suitability"):
        """
        初始化计算器
        
        Parameters:
        -----------
        suitability_data_dir : str
            适宜性数据目录路径
        """
        self.suitability_data_dir = suitability_data_dir
        
        # 技术参数 - 严格按照文献设置
        self.setup_technical_parameters()
        
        # 空间配置
        self.target_resolution = 0.5  # 度
        self.grid_width = 720
        self.grid_height = 360
        
        # 数据缓存
        self.suitability_masks = {}
        self.era5_data = None
        
        print("可再生能源容量因子计算器初始化完成")
        print("基于Zheng et al. & Wang et al. (Nature Communications, 2025)方法论")
    
    def setup_technical_parameters(self):
        """设置技术参数 - 严格按照文献标准"""
        
        # 陆上风电参数 (基于Zheng et al.)
        self.onshore_wind_params = {
            'turbine_model': 'General Electric 2.5 MW',
            'rated_power': 2.5,  # MW
            'hub_height': 100,   # m
            'capacity_density': 2.7,  # MW/km²
            'cut_in_speed': 3.0,      # m/s
            'rated_speed': 12.0,      # m/s
            'cut_out_speed': 25.0,    # m/s
            'power_law_exponent': 1/7,  # 表面摩擦系数
        }
        
        # 海上风电参数 (基于Zheng et al.)
        self.offshore_wind_params = {
            'turbine_model': 'Vestas 8.0 MW',
            'rated_power': 8.0,  # MW
            'hub_height': 100,   # m
            'capacity_density': 4.6,  # MW/km²
            'cut_in_speed': 3.0,      # m/s
            'rated_speed': 13.0,      # m/s
            'cut_out_speed': 25.0,    # m/s
            'power_law_exponent': 1/7,
        }
        
        # 太阳能光伏参数 (基于Zheng et al.)
        self.solar_pv_params = {
            'efficiency': 0.1619,        # 光电转换效率 16.19%
            'system_coef': 0.8056,       # 系统性能系数 80.56%
            'temp_coefficient': 0.005,   # 温度系数 0.005 °C⁻¹
            'rated_power': 161.9,        # W/m² (标准测试条件)
            'capacity_density': 74,      # W/m² (考虑间距)
            'stc_temperature': 25,       # 标准测试条件温度 °C
            'stc_irradiance': 1000,      # 标准测试条件辐照度 W/m²
            'ground_reflectance': 0.2,   # 地面反射率
            # 温度计算系数
            'c1': 4.3,     # °C
            'c2': 0.943,   # 温度系数
            'c3': 0.028,   # °C·m²/W
            'c4': 1.528,   # °C·s/m
        }
        
        # 创建标准功率曲线
        self.create_power_curves()
    
    def create_power_curves(self):
        """创建标准风机功率曲线"""
        
        # GE 2.5MW陆上风机功率曲线 (基于标准数据)
        wind_speeds_onshore = np.array([0, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 30])
        power_output_onshore = np.array([0, 0, 0.1, 0.2, 0.4, 0.6, 0.8, 0.9, 0.95, 0.98, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0, 0])
        
        # Vestas 8.0MW海上风机功率曲线
        wind_speeds_offshore = np.array([0, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 30])
        power_output_offshore = np.array([0, 0, 0.05, 0.15, 0.3, 0.5, 0.7, 0.85, 0.95, 0.98, 0.99, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0, 0])
        
        # 创建插值函数
        self.onshore_power_curve = interp1d(wind_speeds_onshore, power_output_onshore, 
                                          bounds_error=False, fill_value=0)
        self.offshore_power_curve = interp1d(wind_speeds_offshore, power_output_offshore, 
                                           bounds_error=False, fill_value=0)
    
    def load_suitability_masks(self):
        """加载适宜性掩码数据"""
        
        mask_files = {
            'solar': 'solar_final_suitable_areas.tif',
            'wind_onshore': 'onshore_wind_final_suitable_areas.tif',
            'wind_offshore': 'offshore_wind_final_suitable_areas.tif'  # 如果有的话
        }
        
        for tech, filename in mask_files.items():
            filepath = os.path.join(self.suitability_data_dir, filename)
            if os.path.exists(filepath):
                try:
                    with rasterio.open(filepath) as src:
                        self.suitability_masks[tech] = src.read(1)
                        print(f"成功加载{tech}适宜性掩码: {np.sum(self.suitability_masks[tech] > 0):,}个适宜像元")
                except Exception as e:
                    print(f"加载{tech}适宜性掩码失败: {e}")
            else:
                print(f"未找到{tech}适宜性掩码文件: {filepath}")
    
    def calculate_wind_speed_at_hub_height(self, wind_u10, wind_v10, hub_height=100):
        """
        计算轮毂高度风速 - 基于公式(2)和(3)
        
        Parameters:
        -----------
        wind_u10, wind_v10 : array
            10米高度风速分量
        hub_height : float
            轮毂高度
        
        Returns:
        --------
        array
            轮毂高度风速
        """
        # 公式(3): 计算10米高度风速
        wind_speed_10m = np.sqrt(wind_u10**2 + wind_v10**2)
        
        # 公式(2): 轮毂高度风速外推
        alpha = self.onshore_wind_params['power_law_exponent']  # 1/7
        wind_speed_hub = wind_speed_10m * (hub_height / 10)**alpha
        
        return wind_speed_hub
    
    def calculate_wind_capacity_factor(self, wind_speed_hub, technology='onshore'):
        """
        计算风电容量因子 - 基于公式(1)
        
        Parameters:
        -----------
        wind_speed_hub : array
            轮毂高度风速
        technology : str
            'onshore' 或 'offshore'
        
        Returns:
        --------
        array
            容量因子
        """
        if technology == 'onshore':
            power_curve = self.onshore_power_curve
        else:
            power_curve = self.offshore_power_curve
        
        # 应用功率曲线
        capacity_factor = power_curve(wind_speed_hub)
        
        return capacity_factor
    
    def calculate_solar_geometry(self, time, latitude, longitude):
        """
        计算太阳几何角度 - 基于公式(11)和(12)
        
        Parameters:
        -----------
        time : pandas.DatetimeIndex
            时间序列
        latitude : float
            纬度
        longitude : float
            经度
        
        Returns:
        --------
        dict
            太阳几何角度
        """
        # 转换时间
        if hasattr(time, 'values'):
            time_dt = pd.to_datetime(time.values)
        else:
            time_dt = pd.to_datetime(time)
        
        # 年内天数
        day_of_year = time_dt.dayofyear
        
        # 公式(12): 太阳赤纬角
        # 这里需要将角度转换为弧度，因为np.cos()要求输入为弧度制
        declination = -23.45 * np.cos((360 * (day_of_year + 10) / 365.25) * np.pi / 180)
        
        # 计算太阳时角
        hour_of_day = time_dt.hour + time_dt.minute / 60.0
        solar_time = hour_of_day + longitude / 15.0
        hour_angle = 15 * (solar_time - 12)  # 度
        
        # 转换为弧度
        lat_rad = np.radians(latitude)
        dec_rad = np.radians(declination)
        hour_rad = np.radians(hour_angle)
        
        # 公式(11): 太阳天顶角
        cos_zenith = (np.sin(lat_rad) * np.sin(dec_rad) + 
                     np.cos(lat_rad) * np.cos(dec_rad) * np.cos(hour_rad))
        zenith_angle = np.degrees(np.arccos(np.clip(cos_zenith, -1, 1)))
        
        # 太阳高度角
        elevation_angle = 90 - zenith_angle
        
        # 太阳方位角
        cos_azimuth = ((np.sin(dec_rad) * np.cos(lat_rad) - 
                       np.cos(dec_rad) * np.sin(lat_rad) * np.cos(hour_rad)) / 
                      np.sin(np.radians(zenith_angle)))
        azimuth_angle = np.degrees(np.arccos(np.clip(cos_azimuth, -1, 1)))
        
        # 修正方位角象限
        azimuth_angle = np.where(hour_angle > 0, 360 - azimuth_angle, azimuth_angle)
        
        return {
            'zenith_angle': zenith_angle,
            'elevation_angle': elevation_angle,
            'azimuth_angle': azimuth_angle,
            'declination': declination,
            'hour_angle': hour_angle
        }
    
    def calculate_panel_irradiance(self, ghi, solar_geometry, panel_tilt, panel_azimuth=180):
        """
        计算光伏组件辐照度 - 基于公式(6)-(9)
        
        Parameters:
        -----------
        ghi : array
            全球水平辐照度
        solar_geometry : dict
            太阳几何角度
        panel_tilt : float
            组件倾斜角
        panel_azimuth : float
            组件方位角 (南向=180°)
        
        Returns:
        --------
        array
            组件总辐照度
        """
        zenith = solar_geometry['zenith_angle']
        azimuth = solar_geometry['azimuth_angle']
        
        # 公式(10): 直射辐照入射角
        cos_incidence = (np.cos(np.radians(zenith)) * np.cos(np.radians(panel_tilt)) +
                        np.sin(np.radians(zenith)) * np.sin(np.radians(panel_tilt)) *
                        np.cos(np.radians(azimuth - panel_azimuth)))
        cos_incidence = np.maximum(cos_incidence, 0)
        
        # 简化的辐射分解模型
        # 假设直射辐射占总辐射的比例
        clearness_index = np.clip(ghi / 1000, 0, 1)  # 简化的晴空指数
        direct_fraction = np.where(clearness_index > 0.2, 
                                 0.9 * clearness_index, 
                                 0.1)
        diffuse_fraction = 1 - direct_fraction
        
        # 公式(7): 直射辐照度分量
        direct_horizontal = ghi * direct_fraction
        direct_tilted = direct_horizontal * cos_incidence / np.maximum(np.cos(np.radians(zenith)), 0.01)
        
        # 公式(8): 散射辐照度分量 (简化)
        diffuse_horizontal = ghi * diffuse_fraction
        diffuse_tilted = diffuse_horizontal * (1 + np.cos(np.radians(panel_tilt))) / 2
        
        # 公式(9): 反射辐照度分量
        ground_reflectance = self.solar_pv_params['ground_reflectance']
        reflected_tilted = ghi * ground_reflectance * (1 - np.cos(np.radians(panel_tilt))) / 2
        
        # 公式(6): 总辐照度
        total_irradiance = direct_tilted + diffuse_tilted + reflected_tilted
        
        return np.maximum(total_irradiance, 0)
    
    def calculate_cell_temperature(self, ambient_temp, irradiance, wind_speed):
        """
        计算电池工作温度 - 基于公式(14)
        
        Parameters:
        -----------
        ambient_temp : array
            环境温度 (K)
        irradiance : array
            辐照度 (W/m²)
        wind_speed : array
            风速 (m/s)
        
        Returns:
        --------
        array
            电池温度 (°C)
        """
        # 转换为摄氏度
        ambient_temp_c = ambient_temp - 273.15
        
        # 公式(14): 电池工作温度
        c1 = self.solar_pv_params['c1']
        c2 = self.solar_pv_params['c2']
        c3 = self.solar_pv_params['c3']
        c4 = self.solar_pv_params['c4']
        
        cell_temp = c1 + c2 * ambient_temp_c + c3 * irradiance - c4 * wind_speed
        
        return cell_temp
    
    def calculate_solar_capacity_factor(self, irradiance, cell_temperature):
        """
        计算太阳能容量因子 - 基于公式(4)和(5)
        
        Parameters:
        -----------
        irradiance : array
            组件辐照度 (W/m²)
        cell_temperature : array
            电池温度 (°C)
        
        Returns:
        --------
        array
            容量因子
        """
        # 公式(13): 温度修正系数
        gamma = self.solar_pv_params['temp_coefficient']
        t_stc = self.solar_pv_params['stc_temperature']
        temp_coef = 1 - gamma * (cell_temperature - t_stc)
        
        # 公式(5): 实际电力输出
        efficiency = self.solar_pv_params['efficiency']
        system_coef = self.solar_pv_params['system_coef']
        
        power_output = irradiance * efficiency * temp_coef * system_coef
        
        # 公式(4): 容量因子
        rated_power = self.solar_pv_params['rated_power']
        capacity_factor = power_output / rated_power
        
        return np.maximum(capacity_factor, 0)
    
    def calculate_optimal_tilt_angle(self, latitude):
        """计算最优倾斜角"""
        # 简化公式：最优倾斜角约等于纬度
        return abs(latitude)
    
    def process_single_grid(self, grid_i, grid_j, era5_data, year=2020, month=1):
        """
        处理单个网格的容量因子计算
        
        Parameters:
        -----------
        grid_i, grid_j : int
            网格索引
        era5_data : dict
            ERA5数据字典，按年份组织
        year : int
            处理年份
        month : int
            处理月份
        
        Returns:
        --------
        dict
            容量因子结果
        """
        # 计算网格中心坐标
        center_lat = 90 - (grid_i + 0.5) * self.target_resolution
        center_lon = -180 + (grid_j + 0.5) * self.target_resolution
        
        results = {
            'grid_i': grid_i,
            'grid_j': grid_j,
            'latitude': center_lat,
            'longitude': center_lon,
            'year': year,
            'month': month,
            'monthly_data': {}
        }
        
        if year not in era5_data:
            print(f"警告: 年份{year}的ERA5数据不存在")
            return results
        
        year_data = era5_data[year]
        
        # 提取该网格的气象数据
        try:
            # 检查可用的变量
            available_vars = list(year_data.data_vars.keys())
            print(f"网格({grid_i},{grid_j})可用变量: {available_vars}")
            
            # 风速数据
            if 'u10' in year_data and 'v10' in year_data:
                u10 = year_data['u10'].sel(latitude=center_lat, longitude=center_lon, method='nearest')
                v10 = year_data['v10'].sel(latitude=center_lat, longitude=center_lon, method='nearest')
                wind_data_available = True
            else:
                print(f"警告: 网格({grid_i},{grid_j})缺少风速数据")
                wind_data_available = False
            
            # 太阳辐射数据
            if 'ssrd' in year_data:
                ghi = year_data['ssrd'].sel(latitude=center_lat, longitude=center_lon, method='nearest')
                # 转换累积辐射为平均辐射 (J/m² to W/m²)
                ghi = ghi / 3600  # 假设为小时累积值
                solar_data_available = True
            else:
                print(f"警告: 网格({grid_i},{grid_j})缺少太阳辐射数据")
                solar_data_available = False
            
            # 温度数据
            if 't2m' in year_data:
                temp = year_data['t2m'].sel(latitude=center_lat, longitude=center_lon, method='nearest')
            else:
                print(f"警告: 网格({grid_i},{grid_j})缺少温度数据，使用默认值")
                # 使用简单的温度模型
                temp = 288 - 0.0065 * abs(center_lat)  # 基于纬度的简单温度模型
                
        except Exception as e:
            print(f"提取网格({grid_i},{grid_j})年份{year}数据失败: {e}")
            return results
        
        # 计算风电容量因子
        if wind_data_available:
            try:
                wind_speed_hub = self.calculate_wind_speed_at_hub_height(u10, v10)
                wind_cf = self.calculate_wind_capacity_factor(wind_speed_hub, 'onshore')
                results['monthly_data']['wind_cf_mean'] = float(np.mean(wind_cf))
                results['monthly_data']['wind_cf_hourly'] = wind_cf.values if hasattr(wind_cf, 'values') else wind_cf.tolist()
            except Exception as e:
                print(f"风电容量因子计算失败: {e}")
                results['monthly_data']['wind_cf_mean'] = np.nan
        
        # 计算太阳能容量因子
        if solar_data_available:
            try:
                # 计算风速用于温度修正
                if wind_data_available:
                    wind_speed = np.sqrt(u10**2 + v10**2)
                else:
                    wind_speed = 2.0  # 默认风速
                
                solar_geometry = self.calculate_solar_geometry(year_data.time, center_lat, center_lon)
                optimal_tilt = self.calculate_optimal_tilt_angle(center_lat)
                panel_irradiance = self.calculate_panel_irradiance(ghi, solar_geometry, optimal_tilt)
                cell_temp = self.calculate_cell_temperature(temp, panel_irradiance, wind_speed)
                solar_cf = self.calculate_solar_capacity_factor(panel_irradiance, cell_temp)
                
                results['monthly_data']['solar_cf_mean'] = float(np.mean(solar_cf))
                results['monthly_data']['solar_cf_hourly'] = solar_cf.values if hasattr(solar_cf, 'values') else solar_cf.tolist()
            except Exception as e:
                print(f"太阳能容量因子计算失败: {e}")
                results['monthly_data']['solar_cf_mean'] = np.nan
        
        return results

    def validate_results(self, results):
        """
        验证计算结果的合理性

        Parameters:
        -----------
        results : dict
            计算结果

        Returns:
        --------
        dict
            验证报告
        """
        cf = results.get('annual_capacity_factor', 0)

        validation = {
            'is_valid': True,
            'warnings': [],
            'errors': []
        }

        # 检查容量因子范围
        if cf < 0.05:
            validation['warnings'].append(f"容量因子过低: {cf:.3f} < 0.05")
        elif cf > 0.60:
            validation['warnings'].append(f"容量因子过高: {cf:.3f} > 0.60")

        # 检查月度变化
        monthly_cf = results.get('monthly_capacity_factors', [])
        if monthly_cf:
            cf_std = np.std(monthly_cf)
            if cf_std < 0.01:
                validation['warnings'].append("月度容量因子变化过小，可能存在数据问题")

        # 检查小时数据
        hourly_power = results.get('hourly_power_output', [])
        if len(hourly_power) > 0 and np.all(np.array(hourly_power) == 0):
            validation['errors'].append("所有小时功率输出为零")
            validation['is_valid'] = False

        return validation

    def load_era5_data(self, era5_data_dir, year=2020, month=1):
        """
        加载ERA5数据 (支持指定年月)

        Parameters:
        -----------
        era5_data_dir : str
            ERA5数据目录
        year : int
            年份
        month : int
            月份

        Returns:
        --------
        dict
            按年份组织的ERA5数据
        """
        era5_data = {}

        print(f"Loading ERA5 data for {year}-{month:02d}...")

        # 查找指定年月的文件
        potential_patterns = [
            f'era5_wind_{year}_{month:02d}_global.nc',
            f'era5_temperature_{year}_{month:02d}_global.nc', 
            f'era5_solar_radiation_{year}_{month:02d}_global.nc'
        ]

        # 查找文件
        found_files = {}
        for pattern in potential_patterns:
            file_path = os.path.join(era5_data_dir, pattern)
            if os.path.exists(file_path):
                if 'wind' in pattern:
                    found_files['wind'] = file_path
                elif 'temperature' in pattern:
                    found_files['temperature'] = file_path
                elif 'solar' in pattern:
                    found_files['solar'] = file_path
                print(f"  Found: {pattern}")

        if len(found_files) == 0:
            print(f"  Warning: No data files found for {year}-{month:02d}")
            return era5_data

        try:
            # 准备数据字典
            datasets = {}
            
            # 加载风速数据
            if 'wind' in found_files:
                print(f"  Loading wind data: {os.path.basename(found_files['wind'])}")
                wind_ds = xr.open_dataset(found_files['wind'])
                
                # 重命名时间坐标
                if 'valid_time' in wind_ds.coords:
                    wind_ds = wind_ds.rename({'valid_time': 'time'})
                
                datasets['u10'] = wind_ds['u10']
                datasets['v10'] = wind_ds['v10']
                wind_ds.close()
            
            # 加载温度数据
            if 'temperature' in found_files:
                print(f"  Loading temperature data: {os.path.basename(found_files['temperature'])}")
                temp_ds = xr.open_dataset(found_files['temperature'])
                
                # 重命名时间坐标
                if 'valid_time' in temp_ds.coords:
                    temp_ds = temp_ds.rename({'valid_time': 'time'})
                
                datasets['t2m'] = temp_ds['t2m']
                temp_ds.close()
            
            # 加载太阳辐射数据
            if 'solar' in found_files:
                print(f"  Loading solar data: {os.path.basename(found_files['solar'])}")
                solar_ds = xr.open_dataset(found_files['solar'])
                
                # 重命名时间坐标
                if 'valid_time' in solar_ds.coords:
                    solar_ds = solar_ds.rename({'valid_time': 'time'})
                
                datasets['ssrd'] = solar_ds['ssrd']
                solar_ds.close()

            # 创建合并后的数据集
            if len(datasets) >= 3:  # 需要至少3个变量
                year_data = xr.merge(list(datasets.values()))
                era5_data[year] = year_data

                print(f"  Successfully loaded {year}-{month:02d} data")
                print(f"  Variables: {list(datasets.keys())}")
                if 'time' in year_data.dims:
                    print(f"  Time range: {year_data.time.min().values} to {year_data.time.max().values}")
                    print(f"  Hours: {len(year_data.time):,}")
                else:
                    print(f"  Data shape: {year_data.dims}")

            else:
                print(f"  Warning: Insufficient variables found for {year}-{month:02d}")

        except Exception as e:
            print(f"  Failed to load {year}-{month:02d} data: {e}")
            import traceback
            traceback.print_exc()

        return era5_data

    def calculate_capacity_factors_for_suitable_areas(self, era5_data_dir, year=2020, month=1,
                                                    output_dir="outputs", sample_ratio=0.1):
        """
        为所有适宜区域计算容量因子

        Parameters:
        -----------
        era5_data_dir : str
            ERA5数据目录
        year : int
            处理年份
        month : int
            处理月份
        output_dir : str
            输出目录
        sample_ratio : float
            采样比例 (0-1)，用于快速测试
        """
        print("开始计算适宜区域的容量因子...")
        print(f"处理时期: {year}年{month}月")
        print(f"采样比例: {sample_ratio*100:.1f}%")

        # 加载适宜性掩码
        self.load_suitability_masks()

        # 加载ERA5数据
        era5_data = self.load_era5_data(era5_data_dir, year, month)

        if not era5_data:
            print("错误: 未能加载ERA5数据")
            return

        # 获取适宜的网格
        suitable_grids = []

        # 太阳能适宜网格
        if 'solar' in self.suitability_masks:
            solar_mask = self.suitability_masks['solar']
            for i in range(self.grid_height):
                for j in range(self.grid_width):
                    if solar_mask[i, j] > 0:
                        suitable_grids.append((i, j, 'solar'))

        # 风电适宜网格
        if 'wind_onshore' in self.suitability_masks:
            wind_mask = self.suitability_masks['wind_onshore']
            for i in range(self.grid_height):
                for j in range(self.grid_width):
                    if wind_mask[i, j] > 0:
                        suitable_grids.append((i, j, 'wind'))

        print(f"找到 {len(suitable_grids):,} 个适宜网格")

        # 采样处理
        if sample_ratio < 1.0:
            n_sample = int(len(suitable_grids) * sample_ratio)
            indices = np.random.choice(len(suitable_grids), n_sample, replace=False)
            suitable_grids = [suitable_grids[i] for i in indices]
            print(f"采样后处理 {len(suitable_grids):,} 个网格")

        # 处理每个网格
        results = []

        for idx, (grid_i, grid_j, tech_type) in enumerate(suitable_grids):
            if idx % 100 == 0:
                print(f"处理进度: {idx:,}/{len(suitable_grids):,} ({idx/len(suitable_grids)*100:.1f}%)")

            try:
                grid_result = self.process_single_grid(grid_i, grid_j, era5_data, year, month)
                grid_result['technology'] = tech_type
                results.append(grid_result)
            except Exception as e:
                print(f"处理网格({grid_i},{grid_j})失败: {e}")
                continue

        print(f"容量因子计算完成，共处理 {len(results):,} 个网格")

        # 保存结果
        self.save_results(results, output_dir, year, month)

        return results

    def save_hourly_timeseries(self, results, output_dir, year, month):
        """
        保存小时级时间序列数据为CSV格式
        
        Parameters:
        -----------
        results : list
            计算结果列表
        output_dir : str
            输出目录
        year : int
            年份
        month : int
            月份
        """
        print("正在保存小时级时间序列数据...")
        
        # 创建时间索引
        from datetime import datetime, timedelta
        start_date = datetime(year, month, 1)
        
        # 计算该月的天数
        if month == 12:
            end_date = datetime(year + 1, 1, 1)
        else:
            end_date = datetime(year, month + 1, 1)
        
        # 生成小时级时间索引
        time_index = []
        current_time = start_date
        while current_time < end_date:
            time_index.append(current_time)
            current_time += timedelta(hours=1)
        
        print(f"  时间范围: {start_date} 到 {end_date}")
        print(f"  总小时数: {len(time_index)}")
        
        # 准备数据结构
        wind_data_rows = []
        # solar_data_rows = []  # 暂时注释掉
        
        for result in results:
            if 'monthly_data' not in result or not result['monthly_data']:
                continue
                
            monthly_data = result['monthly_data']
            grid_info = {
                'grid_i': result['grid_i'],
                'grid_j': result['grid_j'],
                'latitude': result['latitude'],
                'longitude': result['longitude'],
                'technology': result['technology'],
                'year': result['year'],
                'month': result['month']
            }
            
            # 处理风电数据
            if 'wind_cf_hourly' in monthly_data and result['technology'] == 'wind':
                wind_cf_hourly = monthly_data['wind_cf_hourly']
                if isinstance(wind_cf_hourly, (list, np.ndarray)) and len(wind_cf_hourly) > 0:
                    # 确保数据长度与时间索引匹配
                    cf_data = wind_cf_hourly[:len(time_index)]
                    
                    for i, (timestamp, cf_value) in enumerate(zip(time_index[:len(cf_data)], cf_data)):
                        row = grid_info.copy()
                        row.update({
                            'datetime': timestamp.strftime('%Y-%m-%d %H:%M:%S'),
                            'hour': i,
                            'capacity_factor': float(cf_value) if not np.isnan(cf_value) else 0.0
                        })
                        wind_data_rows.append(row)
            
            # # 处理太阳能数据 - 暂时注释掉
            # if 'solar_cf_hourly' in monthly_data and result['technology'] == 'solar':
            #     solar_cf_hourly = monthly_data['solar_cf_hourly']
            #     if isinstance(solar_cf_hourly, (list, np.ndarray)) and len(solar_cf_hourly) > 0:
            #         cf_data = solar_cf_hourly[:len(time_index)]
            #         
            #         for i, (timestamp, cf_value) in enumerate(zip(time_index[:len(cf_data)], cf_data)):
            #             row = grid_info.copy()
            #             row.update({
            #                 'datetime': timestamp.strftime('%Y-%m-%d %H:%M:%S'),
            #                 'hour': i,
            #                 'capacity_factor': float(cf_value) if not np.isnan(cf_value) else 0.0
            #             })
            #             solar_data_rows.append(row)
        
        # 保存风电时间序列
        if wind_data_rows:
            wind_df = pd.DataFrame(wind_data_rows)
            wind_file = os.path.join(output_dir, f'wind_capacity_factors_hourly_{year}_{month:02d}.csv')
            wind_df.to_csv(wind_file, index=False)
            print(f"  风电小时级数据已保存: {wind_file}")
            print(f"  风电数据行数: {len(wind_df):,}")
            print(f"  涵盖网格数: {wind_df['grid_i'].nunique():,}")
        
        # # 保存太阳能时间序列 - 暂时注释掉
        # if solar_data_rows:
        #     solar_df = pd.DataFrame(solar_data_rows)
        #     solar_file = os.path.join(output_dir, f'solar_capacity_factors_hourly_{year}_{month:02d}.csv')
        #     solar_df.to_csv(solar_file, index=False)
        #     print(f"  太阳能小时级数据已保存: {solar_file}")
        #     print(f"  太阳能数据行数: {len(solar_df):,}")
        
        print("小时级时间序列数据保存完成")

    def save_results(self, results, output_dir, year, month):
        """
        保存计算结果

        Parameters:
        -----------
        results : list
            计算结果列表
        output_dir : str
            输出目录
        year : int
            年份
        month : int
            月份
        """
        os.makedirs(output_dir, exist_ok=True)

        # 创建汇总DataFrame
        summary_data = []

        for result in results:
            base_info = {
                'grid_i': result['grid_i'],
                'grid_j': result['grid_j'],
                'latitude': result['latitude'],
                'longitude': result['longitude'],
                'technology': result['technology'],
                'year': result['year'],
                'month': result['month'],
            }

            # 月度结果
            if 'monthly_data' in result and result['monthly_data']:
                monthly_data = base_info.copy()
                monthly_data.update({
                    'solar_cf_mean': result['monthly_data'].get('solar_cf_mean', np.nan),
                    'wind_cf_mean': result['monthly_data'].get('wind_cf_mean', np.nan),
                })
                summary_data.append(monthly_data)

        # 保存汇总结果
        if summary_data:
            summary_df = pd.DataFrame(summary_data)
            summary_file = os.path.join(output_dir, f'capacity_factors_summary_{year}_{month:02d}.csv')
            summary_df.to_csv(summary_file, index=False)
            print(f"汇总结果已保存到: {summary_file}")

            # 生成统计报告
            self.generate_statistics_report(summary_df, output_dir, year, month)

        # 保存小时级时间序列数据 (CSV格式)
        self.save_hourly_timeseries(results, output_dir, year, month)

        # 保存详细结果 (JSON格式)
        import json
        detailed_file = os.path.join(output_dir, f'capacity_factors_detailed_{year}_{month:02d}.json')
        with open(detailed_file, 'w', encoding='utf-8') as f:
            # 转换numpy数组为列表以便JSON序列化
            json_results = []
            for result in results:
                json_result = result.copy()
                if 'monthly_data' in json_result and json_result['monthly_data']:
                    monthly_data = json_result['monthly_data']
                    for key in ['solar_cf_hourly', 'wind_cf_hourly']:
                        if key in monthly_data:
                            if hasattr(monthly_data[key], 'tolist'):
                                monthly_data[key] = monthly_data[key].tolist()
                            elif isinstance(monthly_data[key], np.ndarray):
                                monthly_data[key] = monthly_data[key].tolist()
                json_results.append(json_result)

            json.dump(json_results, f, indent=2, ensure_ascii=False)
        print(f"详细结果已保存到: {detailed_file}")

    def generate_statistics_report(self, summary_df, output_dir, year, month):
        """
        生成统计报告

        Parameters:
        -----------
        summary_df : pandas.DataFrame
            汇总数据
        output_dir : str
            输出目录
        year : int
            年份
        month : int
            月份
        """
        report_lines = []
        report_lines.append("# 可再生能源容量因子计算报告")
        report_lines.append(f"## 计算时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append(f"## 处理时期: {year}年{month}月")
        report_lines.append(f"## 基于方法: Zheng et al. & Wang et al. (Nature Communications, 2025)")
        report_lines.append("")

        # 基本统计
        report_lines.append("### 基本统计")
        report_lines.append(f"- 总网格数: {len(summary_df):,}")
        # report_lines.append(f"- 太阳能适宜网格: {len(summary_df[summary_df['technology'] == 'solar']):,}")
        report_lines.append(f"- 风电适宜网格: {len(summary_df[summary_df['technology'] == 'wind']):,}")
        report_lines.append("")

        # 太阳能统计
        solar_data = summary_df[summary_df['technology'] == 'solar']['solar_cf_mean'].dropna()
        if len(solar_data) > 0:
            report_lines.append("### 太阳能容量因子统计")
            report_lines.append(f"- 平均值: {solar_data.mean():.3f}")
            report_lines.append(f"- 标准差: {solar_data.std():.3f}")
            report_lines.append(f"- 最小值: {solar_data.min():.3f}")
            report_lines.append(f"- 最大值: {solar_data.max():.3f}")
            report_lines.append(f"- 中位数: {solar_data.median():.3f}")
            report_lines.append("")

        # 风电统计
        wind_data = summary_df[summary_df['technology'] == 'wind']['wind_cf_mean'].dropna()
        if len(wind_data) > 0:
            report_lines.append("### 风电容量因子统计")
            report_lines.append(f"- 平均值: {wind_data.mean():.3f}")
            report_lines.append(f"- 标准差: {wind_data.std():.3f}")
            report_lines.append(f"- 最小值: {wind_data.min():.3f}")
            report_lines.append(f"- 最大值: {wind_data.max():.3f}")
            report_lines.append(f"- 中位数: {wind_data.median():.3f}")
            report_lines.append("")
        
        # 创建可视化
        try:
            self.create_visualization(summary_df, output_dir, year, month)
        except Exception as e:
            print(f"创建可视化图表失败: {e}")

        # 技术参数
        report_lines.append("### 技术参数")
        report_lines.append("#### 陆上风电 (GE 2.5MW)")
        report_lines.append(f"- 额定功率: {self.onshore_wind_params['rated_power']} MW")
        report_lines.append(f"- 轮毂高度: {self.onshore_wind_params['hub_height']} m")
        report_lines.append(f"- 装机密度: {self.onshore_wind_params['capacity_density']} MW/km²")
        report_lines.append("")

        report_lines.append("#### 太阳能光伏")
        report_lines.append(f"- 光电转换效率: {self.solar_pv_params['efficiency']*100:.2f}%")
        report_lines.append(f"- 系统性能系数: {self.solar_pv_params['system_coef']*100:.2f}%")
        report_lines.append(f"- 装机密度: {self.solar_pv_params['capacity_density']} W/m²")

        # 保存报告
        report_file = os.path.join(output_dir, f'capacity_factors_report_{year}_{month:02d}.md')
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(report_lines))

        print(f"统计报告已保存到: {report_file}")

    def create_visualization(self, summary_df, output_dir, year, month):
        """
        创建可视化图表

        Parameters:
        -----------
        summary_df : pandas.DataFrame
            汇总数据
        output_dir : str
            输出目录
        year : int
            年份
        month : int
            月份
        """
        try:
            import matplotlib.pyplot as plt

            fig, axes = plt.subplots(1, 2, figsize=(15, 6))
            fig.suptitle(f'{year}年{month}月风电容量因子分析', fontsize=16)

            # 太阳能容量因子分布 - 暂时注释掉
            # solar_data = summary_df[summary_df['technology'] == 'solar']['solar_cf_mean'].dropna()
            # if len(solar_data) > 0:
            #     axes[0, 0].hist(solar_data, bins=30, alpha=0.7, color='orange')
            #     axes[0, 0].set_title('太阳能容量因子分布')
            #     axes[0, 0].set_xlabel('容量因子')
            #     axes[0, 0].set_ylabel('频数')
            #     axes[0, 0].grid(True, alpha=0.3)

            # 风电容量因子分布
            wind_data = summary_df[summary_df['technology'] == 'wind']['wind_cf_mean'].dropna()
            if len(wind_data) > 0:
                axes[0].hist(wind_data, bins=30, alpha=0.7, color='blue')
                axes[0].set_title('风电容量因子分布')
                axes[0].set_xlabel('容量因子')
                axes[0].set_ylabel('频数')
                axes[0].grid(True, alpha=0.3)

            # 纬度vs容量因子 - 太阳能部分注释掉
            # if len(solar_data) > 0:
            #     solar_subset = summary_df[summary_df['technology'] == 'solar']
            #     axes[1, 0].scatter(solar_subset['latitude'], solar_subset['solar_cf_mean'],
            #                      alpha=0.5, color='orange', s=1)
            #     axes[1, 0].set_title('太阳能容量因子 vs 纬度')
            #     axes[1, 0].set_xlabel('纬度')
            #     axes[1, 0].set_ylabel('太阳能容量因子')
            #     axes[1, 0].grid(True, alpha=0.3)

            if len(wind_data) > 0:
                wind_subset = summary_df[summary_df['technology'] == 'wind']
                axes[1].scatter(wind_subset['latitude'], wind_subset['wind_cf_mean'],
                                 alpha=0.5, color='blue', s=1)
                axes[1].set_title('风电容量因子 vs 纬度')
                axes[1].set_xlabel('纬度')
                axes[1].set_ylabel('风电容量因子')
                axes[1].grid(True, alpha=0.3)

            plt.tight_layout()

            # 保存图表
            viz_file = os.path.join(output_dir, f'capacity_factors_visualization_{year}_{month:02d}.png')
            plt.savefig(viz_file, dpi=300, bbox_inches='tight')
            plt.close()

            print(f"可视化图表已保存到: {viz_file}")

        except Exception as e:
            print(f"创建可视化失败: {e}")
    
    def load_country_boundaries(self, shapefile_path=None):
        """加载国家边界数据"""
        if shapefile_path is None:
            shapefile_path = "/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Solar-Wind/data/gis/countries/ne_50m_admin_0_countries/ne_50m_admin_0_countries.shp"
        
        print(f"\nLoading country boundaries from: {shapefile_path}")
        
        if not os.path.exists(shapefile_path):
            raise FileNotFoundError(f"Country shapefile not found: {shapefile_path}")
        
        countries_gdf = gpd.read_file(shapefile_path)
        
        # 转换到WGS84
        if countries_gdf.crs != 'EPSG:4326':
            countries_gdf = countries_gdf.to_crs('EPSG:4326')
        
        print(f"Loaded {len(countries_gdf)} countries/regions")
        return countries_gdf
    
    def create_country_masks(self, countries_gdf, era5_data):
        """为每个国家创建栅格掩码"""
        print("\nCreating country masks...")
        
        # 获取ERA5网格信息
        lats = era5_data.latitude.values
        lons = era5_data.longitude.values
        
        # 创建变换矩阵
        lat_min, lat_max = lats.min(), lats.max()
        lon_min, lon_max = lons.min(), lons.max()
        
        transform = from_bounds(lon_min, lat_min, lon_max, lat_max, 
                              len(lons), len(lats))
        
        country_masks = {}
        
        for idx, country in tqdm(countries_gdf.iterrows(), 
                               total=len(countries_gdf), 
                               desc="Creating country masks"):
            country_name = country.get('NAME', f'Country_{idx}')
            country_code = country.get('ISO_A3', f'UNK_{idx}')
            
            try:
                if country.geometry is not None and not country.geometry.is_empty:
                    shapes = [(country.geometry, 1)]
                    
                    mask = rasterize(
                        shapes,
                        out_shape=(len(lats), len(lons)),
                        transform=transform,
                        fill=0,
                        dtype=np.uint8,
                        all_touched=True
                    )
                    
                    # 统计像素数量
                    total_pixels = np.sum(mask == 1)
                    
                    if total_pixels > 0:
                        country_masks[country_code] = {
                            'name': country_name,
                            'mask': mask,
                            'total_pixels': total_pixels
                        }
                        
            except Exception as e:
                print(f"Error processing country {country_name}: {str(e)}")
                continue
        
        print(f"Successfully created masks for {len(country_masks)} countries")
        return country_masks
    
    def process_pixels_batch_cf(self, era5_data, lat_indices, lon_indices, latitudes, longitudes):
        """批量处理多个像素的容量因子计算"""
        try:
            # 批量提取像素数据
            u10_batch = era5_data['u10'].isel(latitude=lat_indices, longitude=lon_indices).values
            v10_batch = era5_data['v10'].isel(latitude=lat_indices, longitude=lon_indices).values
            t2m_batch = era5_data['t2m'].isel(latitude=lat_indices, longitude=lon_indices).values
            ssrd_batch = era5_data['ssrd'].isel(latitude=lat_indices, longitude=lon_indices).values
            
            # 获取时间信息
            times = era5_data['time'].values
            
            batch_results = []
            
            for i, (lat, lon) in enumerate(zip(latitudes, longitudes)):
                try:
                    u10 = u10_batch[:, i] if u10_batch.ndim > 1 else u10_batch
                    v10 = v10_batch[:, i] if v10_batch.ndim > 1 else v10_batch
                    t2m = t2m_batch[:, i] if t2m_batch.ndim > 1 else t2m_batch
                    ssrd = ssrd_batch[:, i] if ssrd_batch.ndim > 1 else ssrd_batch
                    
                    # 计算风电容量因子
                    wind_speed_hub = self.calculate_wind_speed_at_hub_height(u10, v10)
                    wind_cf = self.calculate_wind_capacity_factor(wind_speed_hub, 'onshore')
                    
                    # 计算太阳能容量因子
                    # 转换辐射数据 (J/m² to W/m²)
                    ghi = np.diff(np.concatenate([[0], ssrd])) / 3600.0
                    ghi = np.maximum(ghi, 0)
                    
                    # 计算风速用于温度修正
                    wind_speed = np.sqrt(u10**2 + v10**2)
                    
                    solar_geometry = self.calculate_solar_geometry(times, lat, lon)
                    optimal_tilt = self.calculate_optimal_tilt_angle(lat)
                    panel_irradiance = self.calculate_panel_irradiance(ghi, solar_geometry, optimal_tilt)
                    cell_temp = self.calculate_cell_temperature(t2m, panel_irradiance, wind_speed)
                    solar_cf = self.calculate_solar_capacity_factor(panel_irradiance, cell_temp)
                    
                    batch_results.append({
                        'wind_cf': wind_cf,
                        'solar_cf': solar_cf,
                        'times': times
                    })
                    
                except Exception as e:
                    batch_results.append(None)
                    continue
            
            return batch_results
            
        except Exception as e:
            return [None] * len(lat_indices)
    
    def calculate_monthly_country_cf(self, era5_data, country_masks, year=2020, month=1):
        """计算月度国家级容量因子"""
        print(f"\nCalculating country-level CF for {year}-{month:02d}")
        
        # 获取空间维度
        lats = era5_data.latitude.values
        lons = era5_data.longitude.values
        times = era5_data.time.values
        
        # 初始化结果存储
        country_results = {}
        
        print("Processing country-level aggregation...")
        
        for country_code, country_info in tqdm(country_masks.items(), 
                                             desc=f"Processing countries ({month:02d})"):
            country_name = country_info['name']
            mask = country_info['mask']
            
            # 找到该国家的像素位置
            lat_indices, lon_indices = np.where(mask == 1)
            
            if len(lat_indices) == 0:
                continue
            
            # 初始化国家级时间序列
            country_wind_cf = np.zeros(len(times))
            country_solar_cf = np.zeros(len(times))
            valid_pixels = 0
            
            # 处理该国家的所有像素 (采样策略，避免计算过多)
            total_pixels = len(lat_indices)
            if total_pixels > 100:  # 如果像素太多，进行采样
                sample_indices = np.random.choice(total_pixels, 100, replace=False)
                lat_indices = lat_indices[sample_indices]
                lon_indices = lon_indices[sample_indices]
            
            # 批量处理，每次处理20个像素
            batch_size = 20
            for batch_start in range(0, len(lat_indices), batch_size):
                batch_end = min(batch_start + batch_size, len(lat_indices))
                
                batch_lat_indices = lat_indices[batch_start:batch_end]
                batch_lon_indices = lon_indices[batch_start:batch_end]
                batch_latitudes = lats[batch_lat_indices]
                batch_longitudes = lons[batch_lon_indices]
                
                batch_results = self.process_pixels_batch_cf(
                    era5_data, batch_lat_indices, batch_lon_indices, 
                    batch_latitudes, batch_longitudes
                )
                
                for pixel_result in batch_results:
                    if pixel_result is not None:
                        country_wind_cf += pixel_result['wind_cf']
                        country_solar_cf += pixel_result['solar_cf']
                        valid_pixels += 1
            
            # 计算平均值
            if valid_pixels > 0:
                country_wind_cf /= valid_pixels
                country_solar_cf /= valid_pixels
                
                country_results[country_code] = {
                    'name': country_name,
                    'wind_cf': country_wind_cf,
                    'solar_cf': country_solar_cf,
                    'times': times,
                    'valid_pixels': valid_pixels,
                    'total_pixels': country_info['total_pixels']
                }
        
        return country_results
    
    def calculate_annual_country_cf(self, era5_data_dir, year=2020):
        """计算年度国家级容量因子"""
        print(f"\nCalculating annual country-level CF for {year}")
        
        # 加载国家边界
        countries_gdf = self.load_country_boundaries()
        
        # 初始化年度结果存储
        annual_results = {}
        all_times = []
        
        # 逐月处理
        for month in range(1, 13):
            print(f"\n--- Processing month {month}/12 ---")
            
            try:
                # 加载月度ERA5数据
                era5_data = self.load_era5_data(era5_data_dir, year, month)
                
                if not era5_data or year not in era5_data:
                    print(f"Warning: No data for {year}-{month:02d}")
                    continue
                
                # 如果是第一个月，创建国家掩码
                if month == 1:
                    country_masks = self.create_country_masks(countries_gdf, era5_data[year])
                
                # 计算月度结果
                monthly_results = self.calculate_monthly_country_cf(era5_data[year], country_masks, year, month)
                
                # 合并到年度结果
                for country_code, data in monthly_results.items():
                    if country_code not in annual_results:
                        annual_results[country_code] = {
                            'name': data['name'],
                            'wind_cf': [],
                            'solar_cf': [],
                            'times': [],
                            'valid_pixels': data['valid_pixels'],
                            'total_pixels': data['total_pixels']
                        }
                    
                    annual_results[country_code]['wind_cf'].extend(data['wind_cf'])
                    annual_results[country_code]['solar_cf'].extend(data['solar_cf'])
                    annual_results[country_code]['times'].extend(data['times'])
                
                # 收集所有时间点
                if monthly_results:
                    all_times.extend(monthly_results[list(monthly_results.keys())[0]]['times'])
                
                # 清理内存
                del era5_data
                gc.collect()
                
            except Exception as e:
                print(f"Error processing month {month}: {str(e)}")
                continue
        
        # 转换为numpy数组
        for country_code in annual_results:
            annual_results[country_code]['wind_cf'] = np.array(annual_results[country_code]['wind_cf'])
            annual_results[country_code]['solar_cf'] = np.array(annual_results[country_code]['solar_cf'])
            annual_results[country_code]['times'] = np.array(annual_results[country_code]['times'])
        
        return annual_results
    
    def save_country_cf_results(self, results, output_dir, year=2020):
        """保存国家级容量因子结果"""
        print(f"\nSaving country-level CF results for {year}...")
        
        # 创建输出目录
        country_output_dir = os.path.join(output_dir, f"country_cf_{year}")
        os.makedirs(country_output_dir, exist_ok=True)
        
        # 保存详细时间序列数据
        for country_code, data in tqdm(results.items(), desc="Saving country CF data"):
            country_file = os.path.join(country_output_dir, f"{country_code}_{data['name'].replace(' ', '_')}_hourly_cf.npz")
            
            np.savez_compressed(
                country_file,
                wind_cf=data['wind_cf'],
                solar_cf=data['solar_cf'],
                times=data['times'],
                country_code=country_code,
                country_name=data['name'],
                valid_pixels=data['valid_pixels'],
                total_pixels=data['total_pixels']
            )
        
        # 创建汇总统计
        summary_data = []
        for country_code, data in results.items():
            wind_mean = np.mean(data['wind_cf'])
            solar_mean = np.mean(data['solar_cf'])
            
            summary_data.append({
                'Country_Code': country_code,
                'Country_Name': data['name'],
                'Wind_CF_Annual_Mean': wind_mean,
                'Solar_CF_Annual_Mean': solar_mean,
                'Wind_CF_Max': np.max(data['wind_cf']),
                'Wind_CF_Min': np.min(data['wind_cf']),
                'Solar_CF_Max': np.max(data['solar_cf']),
                'Solar_CF_Min': np.min(data['solar_cf']),
                'Valid_Pixels': data['valid_pixels'],
                'Total_Pixels': data['total_pixels'],
                'Data_Coverage': data['valid_pixels'] / data['total_pixels'] if data['total_pixels'] > 0 else 0,
                'Hours_Count': len(data['wind_cf'])
            })
        
        # 保存汇总表
        summary_df = pd.DataFrame(summary_data)
        summary_df = summary_df.sort_values('Wind_CF_Annual_Mean', ascending=False)
        
        summary_file = os.path.join(country_output_dir, f"country_cf_summary_{year}.csv")
        summary_df.to_csv(summary_file, index=False)
        
        # 保存Excel版本（包含8760小时数据）
        excel_file = os.path.join(country_output_dir, f"country_cf_8760h_{year}.xlsx")
        with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
            # 汇总统计表
            summary_df.to_excel(writer, sheet_name='Summary', index=False)
            
            # 选择前10个国家保存详细小时数据
            top_countries = summary_df.head(10)
            for _, row in top_countries.iterrows():
                country_code = row['Country_Code']
                if country_code in results:
                    hourly_data = pd.DataFrame({
                        'DateTime': pd.to_datetime(results[country_code]['times']),
                        'Wind_CF': results[country_code]['wind_cf'],
                        'Solar_CF': results[country_code]['solar_cf']
                    })
                    sheet_name = country_code[:31]  # Excel sheet name limit
                    hourly_data.to_excel(writer, sheet_name=sheet_name, index=False)
        
        print(f"Country-level CF results saved to {country_output_dir}")
        print(f"Summary: {summary_file}")
        print(f"Excel with 8760h data: {excel_file}")
        
        return summary_df
    
    def create_country_cf_visualizations(self, summary_df, output_dir, year=2020):
        """创建国家级容量因子可视化 (英文)"""
        print(f"\nCreating country CF visualizations for {year}...")
        
        # 设置英文字体
        plt.rcParams['font.family'] = 'DejaVu Sans'
        plt.rcParams['axes.unicode_minus'] = False
        
        # 输出目录
        viz_dir = os.path.join(output_dir, f"country_cf_{year}", "visualizations")
        os.makedirs(viz_dir, exist_ok=True)
        
        # 1. 容量因子对比图
        top_20 = summary_df.head(20)
        
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 12))
        
        countries = top_20['Country_Name']
        wind_cf = top_20['Wind_CF_Annual_Mean']
        solar_cf = top_20['Solar_CF_Annual_Mean']
        
        x = np.arange(len(countries))
        
        # 风电CF
        ax1.bar(x, wind_cf, alpha=0.8, color='skyblue')
        ax1.set_title('Wind Capacity Factor by Country (Top 20)', fontsize=14)
        ax1.set_ylabel('Annual Mean Capacity Factor', fontsize=12)
        ax1.set_xticks(x)
        ax1.set_xticklabels(countries, rotation=45, ha='right')
        ax1.grid(True, alpha=0.3)
        ax1.set_ylim(0, max(wind_cf) * 1.1)
        
        # 太阳能CF
        ax2.bar(x, solar_cf, alpha=0.8, color='orange')
        ax2.set_title('Solar PV Capacity Factor by Country (Top 20)', fontsize=14)
        ax2.set_ylabel('Annual Mean Capacity Factor', fontsize=12)
        ax2.set_xticks(x)
        ax2.set_xticklabels(countries, rotation=45, ha='right')
        ax2.grid(True, alpha=0.3)
        ax2.set_ylim(0, max(solar_cf) * 1.1)
        
        plt.tight_layout()
        chart1_file = os.path.join(viz_dir, f"country_cf_comparison_{year}.png")
        plt.savefig(chart1_file, dpi=300, bbox_inches='tight')
        plt.close()
        
        # 2. 散点图 - 风电vs太阳能CF
        fig, ax = plt.subplots(1, 1, figsize=(10, 8))
        
        ax.scatter(summary_df['Wind_CF_Annual_Mean'], 
                  summary_df['Solar_CF_Annual_Mean'], 
                  alpha=0.6, s=60)
        
        ax.set_xlabel('Wind Capacity Factor', fontsize=12)
        ax.set_ylabel('Solar PV Capacity Factor', fontsize=12)
        ax.set_title(f'Wind vs Solar Capacity Factor by Country ({year})', fontsize=14)
        ax.grid(True, alpha=0.3)
        
        # 添加一些国家标签
        top_combined = summary_df.nlargest(10, 'Wind_CF_Annual_Mean')
        for _, row in top_combined.iterrows():
            ax.annotate(row['Country_Code'], 
                       (row['Wind_CF_Annual_Mean'], row['Solar_CF_Annual_Mean']),
                       xytext=(5, 5), textcoords='offset points', fontsize=8)
        
        chart2_file = os.path.join(viz_dir, f"wind_vs_solar_cf_{year}.png")
        plt.savefig(chart2_file, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"Visualizations saved to {viz_dir}")
        return chart1_file, chart2_file

def main():
    """主函数"""
    print("=== 8760-Hour Country-Level Renewable Energy Capacity Factor Calculator ===")
    print("Based on Zheng et al. & Wang et al. (Nature Communications, 2025) methodology")
    print()

    # 初始化计算器
    calculator = RenewableEnergyCalculator()

    # 配置参数 - 使用实际ERA5数据路径
    era5_data_dir = "/Volumes/LaCie/data/era5"  # ERA5数据目录
    year = 2020  # 处理年份
    output_dir = "outputs"

    print(f"Configuration:")
    print(f"- ERA5 data directory: {era5_data_dir}")
    print(f"- Processing year: {year}")
    print(f"- Output directory: {output_dir}")
    print(f"- Country boundaries: Solar-Wind/data/gis/countries/ne_50m_admin_0_countries/")
    print()

    # 检查ERA5数据是否存在
    if not os.path.exists(era5_data_dir):
        print(f"Warning: ERA5 data directory not found: {era5_data_dir}")
        print("Please check the data path or download ERA5 data")
        print()
        print("ERA5 data download instructions:")
        print("1. Register CDS account: https://cds.climate.copernicus.eu/")
        print("2. Configure API key")
        print("3. Download required variables:")
        print("   - Solar: surface_solar_radiation_downwards, 2m_temperature")
        print("   - Wind: 10m_u_component_of_wind, 10m_v_component_of_wind")
        return

    # 列出ERA5数据目录中的文件
    print("ERA5 data directory contents:")
    try:
        files = os.listdir(era5_data_dir)
        nc_files = [f for f in files if f.endswith('.nc') and str(year) in f]
        if nc_files:
            print(f"  Found {len(nc_files)} NetCDF files for {year}:")
            for f in sorted(nc_files)[:10]:  # 只显示前10个文件
                print(f"    {f}")
            if len(nc_files) > 10:
                print(f"    ... and {len(nc_files)-10} more files")
        else:
            print(f"  No NetCDF files found for year {year}")
    except Exception as e:
        print(f"  Cannot list directory: {e}")
    print()

    # 选择计算模式
    print("Available calculation modes:")
    print("1. Country-level 8760-hour CF calculation (Full Year)")
    print("2. Grid-level CF calculation for suitable areas (Demo)")
    print()
    
    mode = input("Select mode (1 or 2, default=1): ").strip()
    if mode == "2":
        # 原有的网格级计算模式
        print("Running grid-level CF calculation...")
        sample_ratio = 0.01  # 采样比例
        
        try:
            results = calculator.calculate_capacity_factors_for_suitable_areas(
                era5_data_dir=era5_data_dir,
                year=year,
                month=1,  # 只计算1月作为演示
                output_dir=output_dir,
                sample_ratio=sample_ratio
            )

            print()
            print("=== Grid-level calculation completed ===")
            print(f"Processed {len(results):,} grids")
            print(f"Results saved to: {output_dir}")

        except Exception as e:
            print(f"Error in grid-level calculation: {e}")
            import traceback
            traceback.print_exc()
    
    else:
        # 国家级8760小时计算模式
        print("Running country-level 8760-hour CF calculation...")
        
        try:
            # 计算年度国家级容量因子
            annual_results = calculator.calculate_annual_country_cf(era5_data_dir, year)
            
            if annual_results:
                # 保存结果
                summary_df = calculator.save_country_cf_results(annual_results, output_dir, year)
                
                # 创建可视化
                chart1, chart2 = calculator.create_country_cf_visualizations(summary_df, output_dir, year)
                
                # 打印汇总统计
                print(f"\n=== 8760-Hour Country CF Calculation Summary for {year} ===")
                print(f"Processed {len(annual_results)} countries")
                print(f"\nTop 10 countries by wind CF:")
                top_wind = summary_df.nlargest(10, 'Wind_CF_Annual_Mean')
                for _, row in top_wind.iterrows():
                    print(f"  {row['Country_Name']:25} | Wind: {row['Wind_CF_Annual_Mean']:.3f} | Solar: {row['Solar_CF_Annual_Mean']:.3f} | Hours: {row['Hours_Count']}")
                
                print(f"\nResults saved:")
                print(f"- Country CF files: {output_dir}/country_cf_{year}/")
                print(f"- Summary: country_cf_summary_{year}.csv")
                print(f"- 8760h Excel: country_cf_8760h_{year}.xlsx")
                print(f"- Visualizations: {os.path.basename(chart1)}, {os.path.basename(chart2)}")
                
                print(f"\n=== Calculation completed successfully! ===")
            else:
                print("Warning: No country-level results generated")

        except Exception as e:
            print(f"Error in country-level calculation: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    main()
