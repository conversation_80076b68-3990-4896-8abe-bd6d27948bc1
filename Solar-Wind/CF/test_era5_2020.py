#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
2020年ERA5数据容量因子计算测试脚本
"""

import os
import sys
import time
from datetime import datetime
import numpy as np
import pandas as pd

# 导入容量因子计算器
from capacity_factor_calculator import RenewableEnergyCalculator

def test_era5_data_loading():
    """测试ERA5数据加载功能"""
    print("=== 测试ERA5数据加载 ===")
    
    calculator = RenewableEnergyCalculator()
    era5_data_dir = "data/era5"
    
    # 测试2020年1月数据
    year = 2020
    month = 1
    
    print(f"测试加载{year}年{month}月数据...")
    era5_data = calculator.load_era5_data(era5_data_dir, year, month)
    
    if era5_data:
        print("✅ 数据加载成功")
        print(f"包含年份: {list(era5_data.keys())}")
        
        if year in era5_data:
            year_data = era5_data[year]
            print(f"变量列表: {list(year_data.data_vars.keys())}")
            print(f"坐标维度: {list(year_data.coords.keys())}")
            
            # 检查数据范围
            for var_name in year_data.data_vars:
                var_data = year_data[var_name]
                print(f"{var_name}: shape={var_data.shape}, min={var_data.min().values:.2f}, max={var_data.max().values:.2f}")
                
    else:
        print("❌ 数据加载失败")
    
    return era5_data

def test_single_grid_calculation():
    """测试单个网格的容量因子计算"""
    print("\n=== 测试单个网格计算 ===")
    
    calculator = RenewableEnergyCalculator()
    era5_data_dir = "data/era5"
    
    # 加载数据
    era5_data = calculator.load_era5_data(era5_data_dir, 2020, 1)
    
    if not era5_data:
        print("❌ 无法加载ERA5数据")
        return
    
    # 测试几个不同的网格点
    test_grids = [
        (180, 360),  # 赤道、0度经线附近
        (100, 200),  # 北纬45度、西经90度附近
        (200, 500),  # 南纬10度、东经90度附近
    ]
    
    for grid_i, grid_j in test_grids:
        print(f"\n测试网格 ({grid_i}, {grid_j}):")
        
        try:
            result = calculator.process_single_grid(grid_i, grid_j, era5_data, 2020, 1)
            
            print(f"  位置: 纬度{result['latitude']:.2f}°, 经度{result['longitude']:.2f}°")
            
            if 'monthly_data' in result and result['monthly_data']:
                monthly = result['monthly_data']
                if 'wind_cf_mean' in monthly:
                    print(f"  风电容量因子: {monthly['wind_cf_mean']:.3f}")
                if 'solar_cf_mean' in monthly:
                    print(f"  太阳能容量因子: {monthly['solar_cf_mean']:.3f}")
            else:
                print("  未计算出有效结果")
                
        except Exception as e:
            print(f"  计算失败: {e}")

def test_monthly_calculation():
    """测试完整月度计算（小规模采样）"""
    print("\n=== 测试月度计算（小规模采样）===")
    
    calculator = RenewableEnergyCalculator()
    
    # 配置参数
    era5_data_dir = "data/era5"
    year = 2020
    month = 1
    output_dir = "outputs/test_results"
    sample_ratio = 0.001  # 只采样0.1%的网格进行快速测试
    
    print(f"测试配置:")
    print(f"- 处理时期: {year}年{month}月")
    print(f"- 采样比例: {sample_ratio*100:.1f}%")
    print(f"- 输出目录: {output_dir}")
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 执行计算
    start_time = time.time()
    
    try:
        results = calculator.calculate_capacity_factors_for_suitable_areas(
            era5_data_dir=era5_data_dir,
            year=year,
            month=month,
            output_dir=output_dir,
            sample_ratio=sample_ratio
        )
        
        elapsed_time = time.time() - start_time
        
        print(f"\n✅ 计算完成!")
        print(f"处理时间: {elapsed_time:.1f} 秒")
        print(f"处理网格数: {len(results):,}")
        
        # 计算统计信息
        if results:
            wind_cfs = []
            for result in results:
                if 'monthly_data' in result and result['monthly_data']:
                    wind_cf = result['monthly_data'].get('wind_cf_mean')
                    if wind_cf is not None and not np.isnan(wind_cf):
                        wind_cfs.append(wind_cf)
            
            if wind_cfs:
                print(f"\n风电容量因子统计:")
                print(f"- 有效样本数: {len(wind_cfs):,}")
                print(f"- 平均值: {np.mean(wind_cfs):.3f}")
                print(f"- 标准差: {np.std(wind_cfs):.3f}")
                print(f"- 最小值: {np.min(wind_cfs):.3f}")
                print(f"- 最大值: {np.max(wind_cfs):.3f}")
                print(f"- 中位数: {np.median(wind_cfs):.3f}")
        
        print(f"\n结果文件保存在: {output_dir}")
        
    except Exception as e:
        print(f"❌ 计算失败: {e}")
        import traceback
        traceback.print_exc()

def test_performance_benchmark():
    """性能基准测试"""
    print("\n=== 性能基准测试 ===")
    
    calculator = RenewableEnergyCalculator()
    era5_data_dir = "data/era5"
    
    # 测试不同采样比例的处理时间
    sample_ratios = [0.0001, 0.001, 0.01]  # 0.01%, 0.1%, 1%
    
    for ratio in sample_ratios:
        print(f"\n测试采样比例: {ratio*100:.2f}%")
        
        start_time = time.time()
        
        try:
            results = calculator.calculate_capacity_factors_for_suitable_areas(
                era5_data_dir=era5_data_dir,
                year=2020,
                month=1,
                output_dir=f"outputs/benchmark_{ratio}",
                sample_ratio=ratio
            )
            
            elapsed_time = time.time() - start_time
            
            print(f"  处理时间: {elapsed_time:.1f} 秒")
            print(f"  处理网格数: {len(results):,}")
            print(f"  平均每网格时间: {elapsed_time/max(len(results), 1):.3f} 秒")
            
        except Exception as e:
            print(f"  测试失败: {e}")

def main():
    """主测试函数"""
    print("🌍 2020年ERA5数据容量因子计算测试")
    print("=" * 50)
    print(f"测试开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 1. 测试数据加载
    era5_data = test_era5_data_loading()
    
    if not era5_data:
        print("❌ 数据加载失败，无法进行后续测试")
        return
    
    # 2. 测试单网格计算
    test_single_grid_calculation()
    
    # 3. 测试小规模月度计算
    test_monthly_calculation()
    
    # 4. 性能基准测试（可选）
    print("\n是否执行性能基准测试？(y/n): ", end="")
    try:
        response = input().strip().lower()
        if response == 'y':
            test_performance_benchmark()
    except KeyboardInterrupt:
        print("\n跳过性能测试")
    
    print("\n" + "=" * 50)
    print(f"测试完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("🎉 所有测试完成!")

if __name__ == "__main__":
    main() 