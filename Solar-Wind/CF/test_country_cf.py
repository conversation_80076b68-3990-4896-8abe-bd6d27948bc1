#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试脚本：计算少数几个国家的8760小时级容量因子
Demo script for country-level capacity factor calculation
"""

from capacity_factor_calculator import RenewableEnergyCalculator
import numpy as np
import os

def test_country_cf():
    """测试几个国家的容量因子计算"""
    print("Demo: Country-Level 8760-Hour CF Calculation for Selected Countries")
    print("="*70)
    
    try:
        # 初始化计算器
        calculator = RenewableEnergyCalculator()
        
        # 配置参数
        era5_data_dir = "/Volumes/LaCie/data/era5"
        year = 2020
        output_dir = "outputs_demo"
        
        # 加载国家边界
        countries_gdf = calculator.load_country_boundaries()
        
        # 选择几个测试国家
        test_countries = ['USA', 'CHN', 'DEU', 'BRA', 'JPN']  # ISO_A3 codes
        print(f"\nTesting countries: {test_countries}")
        
        # 筛选测试国家
        test_gdf = countries_gdf[countries_gdf['ISO_A3'].isin(test_countries)].copy()
        found_countries = test_gdf['ISO_A3'].tolist()
        print(f"Found countries: {found_countries}")
        
        if len(test_gdf) == 0:
            print("No test countries found!")
            return
        
        # 加载第一个月的ERA5数据来创建掩码
        print("\nLoading sample ERA5 data for mask creation...")
        era5_data = calculator.load_era5_data(era5_data_dir, year, 1)
        
        if not era5_data or year not in era5_data:
            print("Error: Could not load ERA5 data")
            return
        
        # 创建国家掩码（只为测试国家）
        print("Creating country masks for test countries...")
        all_country_masks = calculator.create_country_masks(test_gdf, era5_data[year])
        
        # 只保留找到的国家
        test_country_masks = {code: mask for code, mask in all_country_masks.items() 
                            if code in found_countries}
        
        print(f"Created masks for {len(test_country_masks)} countries")
        
        # 计算前3个月的容量因子作为演示
        print(f"\nCalculating CF for first 3 months (demo)...")
        demo_results = {}
        
        for month in range(1, 4):  # 只计算前3个月
            print(f"\n--- Processing month {month}/3 ---")
            
            try:
                # 加载月度数据
                monthly_era5_data = calculator.load_era5_data(era5_data_dir, year, month)
                
                if not monthly_era5_data or year not in monthly_era5_data:
                    print(f"Warning: No data for {year}-{month:02d}")
                    continue
                
                # 计算月度结果
                monthly_results = calculator.calculate_monthly_country_cf(
                    monthly_era5_data[year], test_country_masks, year, month
                )
                
                # 合并到演示结果
                for country_code, data in monthly_results.items():
                    if country_code not in demo_results:
                        demo_results[country_code] = {
                            'name': data['name'],
                            'wind_cf': [],
                            'solar_cf': [],
                            'times': [],
                            'valid_pixels': data['valid_pixels'],
                            'total_pixels': data['total_pixels']
                        }
                    
                    demo_results[country_code]['wind_cf'].extend(data['wind_cf'])
                    demo_results[country_code]['solar_cf'].extend(data['solar_cf'])
                    demo_results[country_code]['times'].extend(data['times'])
                
                # 清理内存
                del monthly_era5_data
                import gc
                gc.collect()
                
            except Exception as e:
                print(f"Error processing month {month}: {str(e)}")
                continue
        
        # 转换为numpy数组
        for country_code in demo_results:
            demo_results[country_code]['wind_cf'] = np.array(demo_results[country_code]['wind_cf'])
            demo_results[country_code]['solar_cf'] = np.array(demo_results[country_code]['solar_cf'])
            demo_results[country_code]['times'] = np.array(demo_results[country_code]['times'])
        
        # 保存演示结果
        if demo_results:
            print(f"\nSaving demo results...")
            os.makedirs(output_dir, exist_ok=True)
            
            summary_df = calculator.save_country_cf_results(demo_results, output_dir, year=2020)
            
            # 创建可视化
            chart1, chart2 = calculator.create_country_cf_visualizations(summary_df, output_dir, year=2020)
            
            # 打印结果
            print(f"\nDemo Results Summary (3 months, ~2160 hours):")
            print("="*60)
            for _, row in summary_df.iterrows():
                hours_count = len(demo_results[row['Country_Code']]['wind_cf'])
                print(f"{row['Country_Name']:20} | Wind CF: {row['Wind_CF_Annual_Mean']:.3f} | "
                      f"Solar CF: {row['Solar_CF_Annual_Mean']:.3f} | Hours: {hours_count}")
            
            print(f"\nDemo completed successfully!")
            print(f"Results saved to: {output_dir}")
            print(f"Charts: {os.path.basename(chart1)}, {os.path.basename(chart2)}")
            print(f"\nTo run full year calculation, use mode 1 in main script")
        else:
            print("No demo results generated")
            
    except Exception as e:
        print(f"Error in demo: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_country_cf() 