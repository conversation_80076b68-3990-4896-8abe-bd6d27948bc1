#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
日本年度光伏容量因子计算
Japan Annual Solar PV Capacity Factor Calculation for 2020

计算流程：
1. 加载日本国家边界
2. 逐月加载ERA5数据和计算光伏容量因子
3. 聚合为年度8760小时时间序列
4. 保存结果和可视化
"""

from capacity_factor_calculator import RenewableEnergyCalculator
import numpy as np
import pandas as pd
import os
import matplotlib.pyplot as plt
from tqdm import tqdm
import gc

def test_japan_annual_solar():
    """计算日本2020年完整光伏容量因子"""
    print("Japan Annual Solar PV Capacity Factor Calculation - 2020")
    print("="*65)
    
    try:
        # 初始化计算器
        calculator = RenewableEnergyCalculator()
        
        # 配置参数
        era5_data_dir = "/Volumes/LaCie/data/era5"
        year = 2020
        output_dir = "outputs_japan_annual"
        
        print(f"Configuration:")
        print(f"- Target country: Japan (JPN)")
        print(f"- Technology: Solar PV only")
        print(f"- Period: Full year {year}")
        print(f"- ERA5 data: {era5_data_dir}")
        print(f"- Output: {output_dir}")
        print(f"- Max pixels per month: 20 (for performance)")
        print()
        
        # 加载国家边界
        print("Loading Japan country boundaries...")
        countries_gdf = calculator.load_country_boundaries()
        japan_gdf = countries_gdf[countries_gdf['ISO_A3'] == 'JPN'].copy()
        
        if len(japan_gdf) == 0:
            print("Error: Japan not found!")
            return
        
        print(f"Japan boundary loaded: {japan_gdf.iloc[0]['NAME']}")
        print()
        
        # 存储年度结果
        annual_cf_data = []
        annual_times = []
        pixel_count_per_month = []
        
        # 逐月处理
        for month in range(1, 13):
            print(f"\n--- Processing Month {month:02d}/12 ---")
            
            try:
                # 加载月度ERA5数据
                era5_data = calculator.load_era5_data(era5_data_dir, year, month)
                
                if not era5_data or year not in era5_data:
                    print(f"Warning: No ERA5 data for {year}-{month:02d}")
                    continue
                
                monthly_data = era5_data[year]
                
                # 第一个月创建国家掩码
                if month == 1:
                    print("Creating Japan country mask...")
                    country_masks = calculator.create_country_masks(japan_gdf, monthly_data)
                    
                    if 'JPN' not in country_masks:
                        print("Error: Japan mask not created!")
                        return
                    
                    japan_mask_info = country_masks['JPN']
                    print(f"Japan mask: {japan_mask_info['total_pixels']} total pixels")
                
                # 计算月度国家级容量因子
                japan_mask = {'JPN': japan_mask_info}
                monthly_result = calculator.calculate_monthly_country_cf(
                    monthly_data, japan_mask, year, month
                )
                
                if 'JPN' in monthly_result:
                    jpn_data = monthly_result['JPN']
                    
                    # 收集月度数据
                    annual_cf_data.extend(jpn_data['solar_cf'])
                    annual_times.extend(jpn_data['times'])
                    pixel_count_per_month.append(jpn_data['valid_pixels'])
                    
                    print(f"Month {month:02d}: {len(jpn_data['solar_cf'])} hours, "
                          f"{jpn_data['valid_pixels']} pixels, "
                          f"mean CF = {np.mean(jpn_data['solar_cf']):.3f}")
                else:
                    print(f"Warning: No results for month {month:02d}")
                    pixel_count_per_month.append(0)
                
                # 清理内存
                del era5_data
                gc.collect()
                
            except Exception as e:
                print(f"Error processing month {month:02d}: {str(e)}")
                pixel_count_per_month.append(0)
                continue
        
        # 检查年度数据
        if len(annual_cf_data) == 0:
            print("Error: No annual data collected!")
            return
        
        # 转换为numpy数组
        annual_cf_array = np.array(annual_cf_data)
        annual_times_array = np.array(annual_times)
        
        print(f"\n=== Annual Results Summary ===")
        print(f"Total hours collected: {len(annual_cf_array)}")
        print(f"Expected hours (8760): {8760}")
        print(f"Data completeness: {len(annual_cf_array)/8760*100:.1f}%")
        print(f"Annual mean CF: {np.mean(annual_cf_array):.3f}")
        print(f"Annual max CF: {np.max(annual_cf_array):.3f}")
        print(f"Annual min CF: {np.min(annual_cf_array):.3f}")
        print(f"Average pixels per month: {np.mean(pixel_count_per_month):.1f}")
        print()
        
        # 保存结果
        print("Saving annual results...")
        os.makedirs(output_dir, exist_ok=True)
        
        # 1. 年度小时级时间序列
        hourly_df = pd.DataFrame({
            'DateTime': pd.to_datetime(annual_times_array),
            'Solar_CF': annual_cf_array
        })
        hourly_df = hourly_df.sort_values('DateTime')  # 确保时间顺序
        
        annual_hourly_file = os.path.join(output_dir, f"japan_solar_8760h_{year}.csv")
        hourly_df.to_csv(annual_hourly_file, index=False)
        print(f"8760h time series: {annual_hourly_file}")
        
        # 2. 年度汇总统计
        summary_data = {
            'Country': 'Japan',
            'Country_Code': 'JPN',
            'Technology': 'Solar PV',
            'Year': year,
            'Total_Hours': len(annual_cf_array),
            'Expected_Hours': 8760,
            'Data_Completeness_Pct': len(annual_cf_array)/8760*100,
            'Annual_Mean_CF': np.mean(annual_cf_array),
            'Annual_Max_CF': np.max(annual_cf_array),
            'Annual_Min_CF': np.min(annual_cf_array),
            'Annual_Std_CF': np.std(annual_cf_array),
            'Q1_CF': np.percentile(annual_cf_array, 25),
            'Q2_CF': np.percentile(annual_cf_array, 50),
            'Q3_CF': np.percentile(annual_cf_array, 75),
            'Avg_Pixels_Per_Month': np.mean(pixel_count_per_month),
            'Total_Pixel_Months': np.sum(pixel_count_per_month)
        }
        
        summary_df = pd.DataFrame([summary_data])
        summary_file = os.path.join(output_dir, f"japan_solar_annual_summary_{year}.csv")
        summary_df.to_csv(summary_file, index=False)
        print(f"Annual summary: {summary_file}")
        
        # 3. 月度统计
        monthly_stats = []
        for month in range(1, 13):
            # 筛选该月数据
            month_mask = pd.to_datetime(annual_times_array).month == month
            if np.any(month_mask):
                month_cf = annual_cf_array[month_mask]
                monthly_stats.append({
                    'Month': month,
                    'Hours': len(month_cf),
                    'Mean_CF': np.mean(month_cf),
                    'Max_CF': np.max(month_cf),
                    'Min_CF': np.min(month_cf),
                    'Std_CF': np.std(month_cf),
                    'Pixels': pixel_count_per_month[month-1] if month-1 < len(pixel_count_per_month) else 0
                })
        
        monthly_df = pd.DataFrame(monthly_stats)
        monthly_file = os.path.join(output_dir, f"japan_solar_monthly_stats_{year}.csv")
        monthly_df.to_csv(monthly_file, index=False)
        print(f"Monthly statistics: {monthly_file}")
        
        # 4. 可视化
        print("Creating visualizations...")
        
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle(f'Japan Solar PV Annual Analysis {year}', fontsize=16)
        
        # 月度平均CF对比
        axes[0,0].bar(monthly_df['Month'], monthly_df['Mean_CF'], color='orange', alpha=0.7)
        axes[0,0].set_title('Monthly Average Capacity Factor')
        axes[0,0].set_xlabel('Month')
        axes[0,0].set_ylabel('Capacity Factor')
        axes[0,0].grid(True, alpha=0.3)
        axes[0,0].set_xticks(range(1, 13))
        
        # 年度时间序列（每周平均）
        hourly_df['Week'] = hourly_df['DateTime'].dt.isocalendar().week
        weekly_cf = hourly_df.groupby('Week')['Solar_CF'].mean()
        axes[0,1].plot(weekly_cf.index, weekly_cf.values, color='blue', linewidth=2)
        axes[0,1].set_title('Weekly Average Capacity Factor')
        axes[0,1].set_xlabel('Week of Year')
        axes[0,1].set_ylabel('Capacity Factor')
        axes[0,1].grid(True, alpha=0.3)
        
        # CF分布直方图
        axes[1,0].hist(annual_cf_array, bins=50, color='green', alpha=0.7)
        axes[1,0].set_title('Annual CF Distribution')
        axes[1,0].set_xlabel('Capacity Factor')
        axes[1,0].set_ylabel('Frequency')
        axes[1,0].grid(True, alpha=0.3)
        
        # 日变化模式（小时平均）
        hourly_df['Hour'] = hourly_df['DateTime'].dt.hour
        hourly_cf = hourly_df.groupby('Hour')['Solar_CF'].mean()
        axes[1,1].plot(hourly_cf.index, hourly_cf.values, color='red', linewidth=2, marker='o')
        axes[1,1].set_title('Diurnal Pattern (Hourly Average)')
        axes[1,1].set_xlabel('Hour of Day')
        axes[1,1].set_ylabel('Capacity Factor')
        axes[1,1].grid(True, alpha=0.3)
        axes[1,1].set_xticks(range(0, 24, 3))
        
        plt.tight_layout()
        viz_file = os.path.join(output_dir, f"japan_solar_annual_analysis_{year}.png")
        plt.savefig(viz_file, dpi=300, bbox_inches='tight')
        plt.close()
        print(f"Visualization: {viz_file}")
        
        print(f"\n=== Japan Annual Solar PV Calculation Completed! ===")
        print(f"Results saved to: {output_dir}")
        print(f"Key findings:")
        print(f"- Data completeness: {len(annual_cf_array)/8760*100:.1f}%")
        print(f"- Annual mean CF: {np.mean(annual_cf_array):.3f} ({np.mean(annual_cf_array)*100:.1f}%)")
        print(f"- Best month: {monthly_df.loc[monthly_df['Mean_CF'].idxmax(), 'Month']} (CF={monthly_df['Mean_CF'].max():.3f})")
        print(f"- Worst month: {monthly_df.loc[monthly_df['Mean_CF'].idxmin(), 'Month']} (CF={monthly_df['Mean_CF'].min():.3f})")
        
    except Exception as e:
        print(f"Error in Japan annual calculation: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_japan_annual_solar()
