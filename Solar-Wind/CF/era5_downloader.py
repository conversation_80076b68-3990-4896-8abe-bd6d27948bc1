#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
ERA5气象数据下载脚本
ERA5 Meteorological Data Downloader

基于Copernicus Climate Data Store (CDS) API下载ERA5再分析数据
支持太阳能和风能评估所需的气象变量

作者: AI Assistant
日期: 2025-08-05
版本: 1.0

使用说明:
1. 注册CDS账户: https://cds.climate.copernicus.eu/user/register
2. 配置API密钥: ~/.cdsapirc
3. 安装依赖: pip install cdsapi
4. 运行脚本: python era5_downloader.py
"""

import os
import sys
import json
import logging
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import warnings

# 第三方库导入
try:
    import cdsapi
except ImportError:
    print("❌ 错误: 未安装cdsapi库")
    print("请运行: pip install cdsapi")
    sys.exit(1)

# 本地配置导入
try:
    from config.cf_config import ERA5_VARIABLES, ERA5_DOWNLOAD_CONFIG
except ImportError:
    print("⚠️ 警告: 无法导入配置文件，使用默认配置")
    ERA5_VARIABLES = {
        'solar': ['surface_solar_radiation_downwards', '2m_temperature'],
        'wind': ['10m_u_component_of_wind', '10m_v_component_of_wind']
    }
    ERA5_DOWNLOAD_CONFIG = {
        'product_type': 'reanalysis',
        'format': 'netcdf',
        'area': [90, -180, -90, 180],
        'grid': [0.25, 0.25],
        'time': [f'{i:02d}:00' for i in range(24)]
    }

warnings.filterwarnings('ignore')

# =============================================================================
# 配置参数
# =============================================================================

# 下载配置
DOWNLOAD_CONFIG = {
    # 时间范围
    'start_year': 2000,
    'end_year': 2024,
    
    # 空间配置
    'spatial_resolution': 0.25,  # 度
    'global_area': [90, -180, -90, 180],  # North, West, South, East
    
    # 存储路径
    'base_output_dir': '/Volumes/LaCie/data/era5',
    'local_backup_dir': 'data/era5',
    
    # 下载策略
    'download_by_month': True,  # 按月下载
    'max_retries': 3,
    'retry_delay': 300,  # 5分钟
    
    # 文件命名
    'file_naming': {
        'solar_radiation': 'era5_solar_radiation_{year}_{month:02d}_global.nc',
        'temperature': 'era5_temperature_{year}_{month:02d}_global.nc',
        'wind': 'era5_wind_{year}_{month:02d}_global.nc',
        'combined': 'era5_combined_{year}_{month:02d}_global.nc'
    }
}

# 必需的ERA5变量（拆分为独立数据类型）
REQUIRED_VARIABLES = {
    'solar_radiation': [
        'surface_solar_radiation_downwards',  # 地表太阳辐射下行 (J/m²)
    ],
    'temperature': [
        '2m_temperature',                     # 2米高度温度 (K)
    ],
    'wind': [
        '10m_u_component_of_wind',           # 10米u风分量 (m/s)
        '10m_v_component_of_wind',           # 10米v风分量 (m/s)
    ]
}

# =============================================================================
# 日志配置
# =============================================================================

def setup_logging(log_dir: str = "logs") -> logging.Logger:
    """设置日志系统"""
    
    # 创建日志目录
    Path(log_dir).mkdir(exist_ok=True)
    
    # 配置日志格式
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    date_format = '%Y-%m-%d %H:%M:%S'
    
    # 创建logger
    logger = logging.getLogger('era5_downloader')
    logger.setLevel(logging.INFO)
    
    # 清除已有的处理器
    logger.handlers.clear()
    
    # 文件处理器
    log_file = Path(log_dir) / f"era5_download_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setLevel(logging.INFO)
    file_formatter = logging.Formatter(log_format, date_format)
    file_handler.setFormatter(file_formatter)
    
    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_formatter = logging.Formatter('%(levelname)s - %(message)s')
    console_handler.setFormatter(console_formatter)
    
    # 添加处理器
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    return logger

# =============================================================================
# ERA5下载器类
# =============================================================================

class ERA5Downloader:
    """ERA5数据下载器"""
    
    def __init__(self, config: Dict = None):
        """
        初始化下载器
        
        Parameters:
        -----------
        config : dict, optional
            下载配置参数
        """
        self.config = config or DOWNLOAD_CONFIG
        self.logger = setup_logging()
        
        # 初始化CDS客户端
        try:
            self.cds_client = cdsapi.Client()
            self.logger.info("✅ CDS客户端初始化成功")
        except Exception as e:
            self.logger.error(f"❌ CDS客户端初始化失败: {e}")
            self.logger.error("请检查 ~/.cdsapirc 配置文件")
            raise
        
        # 创建输出目录
        self.setup_directories()
        
        # 加载下载状态
        self.download_status = self.load_download_status()
    
    def setup_directories(self):
        """创建必要的目录结构"""
        
        directories = [
            self.config['base_output_dir'],
            self.config['local_backup_dir'],
            'logs',
            'status'
        ]
        
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
            self.logger.info(f"📁 创建目录: {directory}")
    
    def load_download_status(self) -> Dict:
        """加载下载状态"""
        
        status_file = Path('status/download_status.json')
        
        if status_file.exists():
            try:
                with open(status_file, 'r', encoding='utf-8') as f:
                    status = json.load(f)
                self.logger.info(f"📋 加载下载状态: {len(status)} 条记录")
                return status
            except Exception as e:
                self.logger.warning(f"⚠️ 加载下载状态失败: {e}")
        
        return {}
    
    def save_download_status(self):
        """保存下载状态"""
        
        status_file = Path('status/download_status.json')
        
        try:
            with open(status_file, 'w', encoding='utf-8') as f:
                json.dump(self.download_status, f, indent=2, ensure_ascii=False)
            self.logger.info("💾 下载状态已保存")
        except Exception as e:
            self.logger.error(f"❌ 保存下载状态失败: {e}")

    def check_existing_files(self) -> Dict[str, List[str]]:
        """检查已存在的文件"""

        existing_files = {'solar_radiation': [], 'temperature': [], 'wind': [], 'combined': []}

        # 检查主输出目录
        base_dir = Path(self.config['base_output_dir'])
        if base_dir.exists():
            for file_type in ['solar_radiation', 'temperature', 'wind', 'combined']:
                pattern = self.config['file_naming'][file_type].replace('{year}', '*').replace('{month:02d}', '*')
                files = list(base_dir.glob(pattern))
                existing_files[file_type].extend([f.name for f in files])

        # 检查本地备份目录
        local_dir = Path(self.config['local_backup_dir'])
        if local_dir.exists():
            for file_type in ['solar_radiation', 'temperature', 'wind', 'combined']:
                pattern = self.config['file_naming'][file_type].replace('{year}', '*').replace('{month:02d}', '*')
                files = list(local_dir.glob(pattern))
                for f in files:
                    if f.name not in existing_files[file_type]:
                        existing_files[file_type].append(f.name)

        # 检查旧格式的太阳能文件（向后兼容）
        old_solar_patterns = ['era5_solar_*_global.nc', 'era5_solar_*_*_global.nc']
        for pattern in old_solar_patterns:
            if base_dir.exists():
                old_files = list(base_dir.glob(pattern))
                for f in old_files:
                    if f.name not in existing_files['solar_radiation']:
                        existing_files['solar_radiation'].append(f.name)
            if local_dir.exists():
                old_files = list(local_dir.glob(pattern))
                for f in old_files:
                    if f.name not in existing_files['solar_radiation']:
                        existing_files['solar_radiation'].append(f.name)

        # 记录统计信息
        total_files = sum(len(files) for files in existing_files.values())
        self.logger.info(f"📊 已存在文件统计:")
        self.logger.info(f"  太阳辐射数据: {len(existing_files['solar_radiation'])} 个文件")
        self.logger.info(f"  温度数据: {len(existing_files['temperature'])} 个文件")
        self.logger.info(f"  风能数据: {len(existing_files['wind'])} 个文件")
        self.logger.info(f"  组合数据: {len(existing_files['combined'])} 个文件")
        self.logger.info(f"  总计: {total_files} 个文件")

        return existing_files

    def is_file_downloaded(self, year: int, month: int, data_type: str) -> bool:
        """检查指定文件是否已下载"""

        filename = self.config['file_naming'][data_type].format(year=year, month=month)

        # 检查主输出目录
        main_file = Path(self.config['base_output_dir']) / filename
        if main_file.exists() and main_file.stat().st_size > 1024 * 1024:  # 至少1MB
            return True

        # 检查本地备份目录
        backup_file = Path(self.config['local_backup_dir']) / filename
        if backup_file.exists() and backup_file.stat().st_size > 1024 * 1024:
            return True

        # 检查下载状态记录
        status_key = f"{data_type}_{year}_{month:02d}"
        if status_key in self.download_status:
            status = self.download_status[status_key]
            if status.get('status') == 'completed' and status.get('file_size', 0) > 1024 * 1024:
                return True

        return False

    def generate_download_request(self, year: int, month: int, data_type: str) -> Dict:
        """生成下载请求参数"""

        # 基础请求参数
        request = {
            'product_type': 'reanalysis',
            'format': 'netcdf',
            'area': self.config['global_area'],
            'grid': [self.config['spatial_resolution'], self.config['spatial_resolution']],
            'year': str(year),
            'month': f'{month:02d}',
            'day': [f'{i:02d}' for i in range(1, 32)],  # 所有天数
            'time': [f'{i:02d}:00' for i in range(24)],  # 24小时
        }

        # 添加变量
        if data_type in REQUIRED_VARIABLES:
            request['variable'] = REQUIRED_VARIABLES[data_type]
        else:
            raise ValueError(f"未知的数据类型: {data_type}")

        return request

    def download_single_file(self, year: int, month: int, data_type: str) -> bool:
        """下载单个文件"""

        # 检查是否已下载
        if self.is_file_downloaded(year, month, data_type):
            self.logger.info(f"⏭️ 跳过已存在文件: {data_type} {year}-{month:02d}")
            return True

        # 生成文件名和路径
        filename = self.config['file_naming'][data_type].format(year=year, month=month)
        output_path = Path(self.config['base_output_dir']) / filename

        # 生成下载请求
        try:
            request = self.generate_download_request(year, month, data_type)
            self.logger.info(f"📥 开始下载: {filename}")
            self.logger.info(f"   变量: {', '.join(request['variable'])}")
            self.logger.info(f"   时间: {year}-{month:02d}")

            # 执行下载
            start_time = time.time()
            self.cds_client.retrieve('reanalysis-era5-single-levels', request, str(output_path))
            download_time = time.time() - start_time

            # 检查文件大小
            if output_path.exists():
                file_size = output_path.stat().st_size
                file_size_mb = file_size / (1024 * 1024)

                if file_size > 1024 * 1024:  # 至少1MB
                    self.logger.info(f"✅ 下载完成: {filename}")
                    self.logger.info(f"   文件大小: {file_size_mb:.1f} MB")
                    self.logger.info(f"   下载时间: {download_time:.1f} 秒")

                    # 更新下载状态
                    status_key = f"{data_type}_{year}_{month:02d}"
                    self.download_status[status_key] = {
                        'status': 'completed',
                        'filename': filename,
                        'file_size': file_size,
                        'download_time': download_time,
                        'timestamp': datetime.now().isoformat()
                    }
                    self.save_download_status()

                    return True
                else:
                    self.logger.error(f"❌ 文件太小，可能下载失败: {filename} ({file_size_mb:.1f} MB)")
                    output_path.unlink()  # 删除无效文件
                    return False
            else:
                self.logger.error(f"❌ 下载失败，文件不存在: {filename}")
                return False

        except Exception as e:
            self.logger.error(f"❌ 下载异常: {filename}")
            self.logger.error(f"   错误信息: {str(e)}")

            # 清理可能的不完整文件
            if output_path.exists():
                output_path.unlink()

            return False

    def download_with_retry(self, year: int, month: int, data_type: str) -> bool:
        """带重试机制的下载"""

        max_retries = self.config['max_retries']
        retry_delay = self.config['retry_delay']

        for attempt in range(max_retries):
            try:
                if self.download_single_file(year, month, data_type):
                    return True

                if attempt < max_retries - 1:
                    self.logger.warning(f"⚠️ 下载失败，{retry_delay}秒后重试 (尝试 {attempt + 1}/{max_retries})")
                    time.sleep(retry_delay)

            except KeyboardInterrupt:
                self.logger.info("⏹️ 用户中断下载")
                raise
            except Exception as e:
                self.logger.error(f"❌ 下载异常 (尝试 {attempt + 1}/{max_retries}): {str(e)}")
                if attempt < max_retries - 1:
                    time.sleep(retry_delay)

        self.logger.error(f"❌ 下载最终失败: {data_type} {year}-{month:02d}")
        return False

    def download_year_data(self, year: int, data_types: List[str] = None) -> Dict[str, int]:
        """下载指定年份的数据"""

        if data_types is None:
            data_types = ['solar_radiation', 'temperature', 'wind']

        results = {data_type: 0 for data_type in data_types}
        total_files = len(data_types) * 12
        completed_files = 0

        self.logger.info(f"🗓️ 开始下载 {year} 年数据")
        self.logger.info(f"   数据类型: {', '.join(data_types)}")
        self.logger.info(f"   总文件数: {total_files}")

        for month in range(1, 13):
            for data_type in data_types:
                self.logger.info(f"📅 处理: {year}-{month:02d} {data_type}")

                if self.download_with_retry(year, month, data_type):
                    results[data_type] += 1
                    completed_files += 1

                    # 显示进度
                    progress = (completed_files / total_files) * 100
                    self.logger.info(f"📊 进度: {completed_files}/{total_files} ({progress:.1f}%)")
                else:
                    self.logger.error(f"❌ 下载失败: {year}-{month:02d} {data_type}")

        # 总结
        self.logger.info(f"📋 {year} 年下载总结:")
        for data_type in data_types:
            success_rate = (results[data_type] / 12) * 100
            self.logger.info(f"   {data_type}: {results[data_type]}/12 ({success_rate:.1f}%)")

        return results

    def download_range_data(self, start_year: int = None, end_year: int = None,
                          data_types: List[str] = None) -> Dict[int, Dict[str, int]]:
        """下载指定年份范围的数据"""

        start_year = start_year or self.config['start_year']
        end_year = end_year or self.config['end_year']
        data_types = data_types or ['solar_radiation', 'temperature', 'wind']

        total_years = end_year - start_year + 1
        results = {}

        self.logger.info(f"🌍 开始批量下载ERA5数据")
        self.logger.info(f"   时间范围: {start_year}-{end_year} ({total_years} 年)")
        self.logger.info(f"   数据类型: {', '.join(data_types)}")
        self.logger.info(f"   输出目录: {self.config['base_output_dir']}")

        start_time = time.time()

        for year_idx, year in enumerate(range(start_year, end_year + 1)):
            year_start_time = time.time()

            self.logger.info(f"\n{'='*60}")
            self.logger.info(f"📅 处理年份: {year} ({year_idx + 1}/{total_years})")
            self.logger.info(f"{'='*60}")

            year_results = self.download_year_data(year, data_types)
            results[year] = year_results

            year_time = time.time() - year_start_time
            self.logger.info(f"⏱️ {year} 年处理时间: {year_time:.1f} 秒")

            # 估算剩余时间
            if year_idx > 0:
                avg_time_per_year = (time.time() - start_time) / (year_idx + 1)
                remaining_years = total_years - year_idx - 1
                estimated_remaining = avg_time_per_year * remaining_years
                self.logger.info(f"⏳ 预计剩余时间: {estimated_remaining:.0f} 秒")

        total_time = time.time() - start_time
        self.logger.info(f"\n🎉 批量下载完成!")
        self.logger.info(f"   总耗时: {total_time:.1f} 秒 ({total_time/3600:.1f} 小时)")

        return results

    def generate_download_summary(self) -> str:
        """生成下载摘要报告"""

        summary = []
        summary.append("📊 ERA5数据下载摘要报告")
        summary.append("=" * 50)
        summary.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        summary.append("")

        # 配置信息
        summary.append("⚙️ 下载配置:")
        summary.append(f"  时间范围: {self.config['start_year']}-{self.config['end_year']}")
        summary.append(f"  空间分辨率: {self.config['spatial_resolution']}°")
        summary.append(f"  输出目录: {self.config['base_output_dir']}")
        summary.append("")

        # 检查现有文件
        existing_files = self.check_existing_files()
        summary.append("📁 现有文件统计:")
        for data_type, files in existing_files.items():
            summary.append(f"  {data_type}: {len(files)} 个文件")
        summary.append("")

        # 下载状态统计
        if self.download_status:
            completed = sum(1 for status in self.download_status.values()
                          if status.get('status') == 'completed')
            total_size = sum(status.get('file_size', 0) for status in self.download_status.values()
                           if status.get('status') == 'completed')
            total_size_gb = total_size / (1024**3)

            summary.append("📈 下载状态统计:")
            summary.append(f"  已完成: {completed} 个文件")
            summary.append(f"  总大小: {total_size_gb:.2f} GB")
            summary.append("")

        return "\n".join(summary)

# =============================================================================
# 辅助函数
# =============================================================================

def check_cds_configuration():
    """检查CDS配置"""

    cdsapirc_path = Path.home() / '.cdsapirc'

    if not cdsapirc_path.exists():
        print("❌ 错误: 未找到CDS配置文件")
        print("\n请按以下步骤配置:")
        print("1. 注册CDS账户: https://cds.climate.copernicus.eu/user/register")
        print("2. 获取API密钥: https://cds.climate.copernicus.eu/api-how-to")
        print("3. 创建配置文件 ~/.cdsapirc，内容如下:")
        print("   url: https://cds.climate.copernicus.eu/api/v2")
        print("   key: YOUR_API_KEY_HERE")
        return False

    try:
        with open(cdsapirc_path, 'r') as f:
            content = f.read()
            if 'url:' in content and 'key:' in content:
                print("✅ CDS配置文件检查通过")
                return True
            else:
                print("❌ CDS配置文件格式错误")
                return False
    except Exception as e:
        print(f"❌ 读取CDS配置文件失败: {e}")
        return False

def print_usage_instructions():
    """打印使用说明"""

    print("\n📖 使用说明:")
    print("=" * 50)
    print("1. 环境准备:")
    print("   pip install cdsapi")
    print("   注册CDS账户并配置API密钥")
    print("")
    print("2. 运行方式:")
    print("   python era5_downloader.py                    # 交互式运行")
    print("   python era5_downloader.py --year 2020       # 下载指定年份")
    print("   python era5_downloader.py --range 2020 2022 # 下载年份范围")
    print("   python era5_downloader.py --check           # 检查现有文件")
    print("")
    print("3. 数据说明:")
    print("   - 太阳辐射变量: surface_solar_radiation_downwards")
    print("   - 温度变量: 2m_temperature")
    print("   - 风能变量: 10m_u_component_of_wind, 10m_v_component_of_wind")
    print("   - 时间分辨率: 小时级 (8760小时/年)")
    print("   - 空间分辨率: 0.25° × 0.25°")
    print("   - 覆盖范围: 全球")
    print("")
    print("4. 存储位置:")
    print(f"   主目录: {DOWNLOAD_CONFIG['base_output_dir']}")
    print(f"   备份目录: {DOWNLOAD_CONFIG['local_backup_dir']}")

# =============================================================================
# 主函数
# =============================================================================

def main():
    """主函数"""

    print("🌍 ERA5气象数据下载器")
    print("=" * 50)

    # 检查CDS配置
    if not check_cds_configuration():
        return

    # 解析命令行参数
    import argparse
    parser = argparse.ArgumentParser(description='ERA5气象数据下载器')
    parser.add_argument('--year', type=int, help='下载指定年份的数据')
    parser.add_argument('--range', nargs=2, type=int, metavar=('START', 'END'),
                       help='下载年份范围的数据')
    parser.add_argument('--types', nargs='+', choices=['solar_radiation', 'temperature', 'wind'],
                       default=['solar_radiation', 'temperature', 'wind'], help='数据类型')
    parser.add_argument('--check', action='store_true', help='检查现有文件')
    parser.add_argument('--summary', action='store_true', help='生成摘要报告')

    args = parser.parse_args()

    try:
        # 初始化下载器
        downloader = ERA5Downloader()

        if args.check:
            # 检查现有文件
            existing_files = downloader.check_existing_files()
            print("\n📊 文件检查完成")

        elif args.summary:
            # 生成摘要报告
            summary = downloader.generate_download_summary()
            print(summary)

            # 保存报告
            report_file = f"era5_download_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(summary)
            print(f"\n📄 报告已保存: {report_file}")

        elif args.year:
            # 下载指定年份
            results = downloader.download_year_data(args.year, args.types)
            print(f"\n✅ {args.year} 年数据下载完成")

        elif args.range:
            # 下载年份范围
            start_year, end_year = args.range
            results = downloader.download_range_data(start_year, end_year, args.types)
            print(f"\n✅ {start_year}-{end_year} 年数据下载完成")

        else:
            # 交互式模式
            print_usage_instructions()

            while True:
                print("\n🔧 请选择操作:")
                print("1. 下载指定年份数据")
                print("2. 下载年份范围数据")
                print("3. 检查现有文件")
                print("4. 生成摘要报告")
                print("5. 显示使用说明")
                print("0. 退出")

                choice = input("\n请输入选择 (0-5): ").strip()

                if choice == '0':
                    print("👋 再见!")
                    break
                elif choice == '1':
                    year = int(input("请输入年份 (2000-2024): "))
                    if 2000 <= year <= 2024:
                        print("可选数据类型: solar_radiation, temperature, wind")
                        types_input = input("请输入数据类型 (用空格分隔，回车使用默认): ").strip()
                        if types_input:
                            data_types = types_input.split()
                            valid_types = ['solar_radiation', 'temperature', 'wind']
                            data_types = [t for t in data_types if t in valid_types]
                            if not data_types:
                                print("❌ 无效的数据类型，使用默认设置")
                                data_types = None
                        else:
                            data_types = None
                        downloader.download_year_data(year, data_types)
                    else:
                        print("❌ 年份超出范围")
                elif choice == '2':
                    start_year = int(input("请输入起始年份: "))
                    end_year = int(input("请输入结束年份: "))
                    if 2000 <= start_year <= end_year <= 2024:
                        print("可选数据类型: solar_radiation, temperature, wind")
                        types_input = input("请输入数据类型 (用空格分隔，回车使用默认): ").strip()
                        if types_input:
                            data_types = types_input.split()
                            valid_types = ['solar_radiation', 'temperature', 'wind']
                            data_types = [t for t in data_types if t in valid_types]
                            if not data_types:
                                print("❌ 无效的数据类型，使用默认设置")
                                data_types = None
                        else:
                            data_types = None
                        downloader.download_range_data(start_year, end_year, data_types)
                    else:
                        print("❌ 年份范围无效")
                elif choice == '3':
                    downloader.check_existing_files()
                elif choice == '4':
                    summary = downloader.generate_download_summary()
                    print(summary)
                elif choice == '5':
                    print_usage_instructions()
                else:
                    print("❌ 无效选择")

    except KeyboardInterrupt:
        print("\n⏹️ 用户中断操作")
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
