# 全球202个国家技术成本参数数据集

## 📋 数据集概览

### 🎯 **核心文件**: `Global_202_Countries_Technology_Cost_Parameters.xlsx`

这是一个完整的全球电力系统技术成本参数数据集，覆盖**202个国家**的**9种发电技术**的**3类成本参数**，总计**1818行**完整数据。

## 📊 数据集特点

### ✅ **完整性**
- **国家覆盖**: 202个国家（全球完整覆盖）
- **技术覆盖**: 9种技术类型（核电、水电、煤电、石油、天然气、风电、太阳能、短期储能、长期储能）
- **参数覆盖**: 3类成本参数（投资成本、固定运维成本、可变运维成本）
- **数据完整性**: 100%（无缺失值）

### 🌍 **标准化**
- **国家代码**: 使用ISO 3166-1 alpha-3标准三位国家代码
- **区域分类**: R10区域分类系统（10个区域）
- **单位标准化**: 明确标注各参数的计量单位
- **数据格式**: 统一的表格结构，便于编程处理

### 📈 **数据质量分级**

| 数据来源类型 | 国家数量 | 占比 | 数据质量 | 说明 |
|------------|---------|------|----------|------|
| **原始数据** | 44个 | 21.8% | ⭐⭐⭐⭐⭐ | 来自ST14+ST15的文献原始数据 |
| **区域平均** | 117个 | 57.9% | ⭐⭐⭐⭐ | 基于同区域有数据国家的平均值 |
| **全球平均** | 41个 | 20.3% | ⭐⭐⭐ | 基于全球44个有数据国家的平均值 |

## 📁 工作表结构

### 1. **全球技术成本参数** (主数据表)
- **1818行数据**: 202个国家 × 9种技术
- **10个字段**: ISO代码、国家名称、技术类型、成本参数、单位信息
- **按ISO代码排序**: 便于查找和使用

### 2. **数据来源**
- **202个国家**的数据来源分类
- **数据质量评级**和可靠性说明
- **区域信息**和填充方法标识

### 3. **区域统计**
- **10个区域**的数据覆盖情况
- **填充方法**和数据质量评估
- **区域代表性**分析

### 4. **参数统计**
- **27个参数**的分布统计（9技术×3参数）
- **统计指标**: 最小值、最大值、平均值、中位数、标准差、变异系数
- **参数合理性**验证

### 5. **填充方法说明**
- **三种填充方法**的详细说明
- **适用情况**和可靠性分析
- **使用建议**

### 6. **数据说明**
- **完整的元数据**信息
- **数据处理流程**说明
- **质量控制**措施
- **使用指南**

## 🔧 创建流程

### 第一步：标准化ST9国家区域映射
- ✅ 将178个国家扩展到202个国家
- ✅ 统一使用ISO 3位国家代码
- ✅ 标记44个有原始数据的国家
- ✅ 完善区域分类映射

### 第二步：参数填充策略
- ✅ 区域代表性分析（7个区域有代表国家，3个区域无代表）
- ✅ 计算区域平均值（基于同区域有数据国家）
- ✅ 计算全球平均值（基于44个有数据国家）
- ✅ 智能填充策略（优先区域平均，次选全球平均）

### 第三步：生成完整Excel文件
- ✅ 6个工作表的完整数据集
- ✅ 数据验证和质量检查
- ✅ 详细的说明文档

## 🎯 使用建议

### 1. **优先级使用顺序**
1. **原始数据国家** (44个) - 最高可靠性
2. **区域平均国家** (117个) - 较高可靠性  
3. **全球平均国家** (41个) - 中等可靠性

### 2. **模型应用**
- **直接导入**: 标准化格式便于编程处理
- **国家匹配**: 使用ISO代码避免名称不一致
- **参数验证**: 参考参数统计表进行合理性检查
- **敏感性分析**: 基于数据质量分级进行不确定性分析

### 3. **数据更新**
- **建议频率**: 每年更新一次
- **更新来源**: 跟踪最新的技术成本文献
- **质量控制**: 保持填充策略的一致性

## 📚 相关文件

### 核心数据文件
- `Global_202_Countries_Technology_Cost_Parameters.xlsx` - **主要数据集**
- `Zheng_ST14_ST15_合并技术成本参数.xlsx` - 44个国家原始数据
- `Zheng_ST9_标准化国家区域映射.xlsx` - 标准化区域映射

### 处理脚本
- `fill_global_cost_parameters.py` - 完整的数据处理脚本
- `standardize_st9_mapping.py` - ST9标准化脚本
- `create_zheng_excel_tables.py` - 原始数据提取脚本

### 说明文档
- `Zheng_ST_文件说明.md` - 原始数据说明
- `Global_202_Countries_数据集说明.md` - 本文档

## 🔍 数据验证

### ✅ **完整性检查**
- 总行数: 1818 (202国家 × 9技术) ✅
- 缺失值: 0个 ✅
- 国家覆盖: 202/202 ✅
- 技术覆盖: 9/9 ✅

### ✅ **合理性检查**
- 投资成本范围: [135.0, 6920.0] $/kW ✅
- 固定运维成本范围: [2.7, 138.4] $/kW/year ✅
- 可变运维成本范围: [0.0, 67.0] $/MWh ✅
- 负值检查: 0个负值 ✅

### ✅ **一致性检查**
- ISO代码标准: 符合ISO 3166-1 alpha-3 ✅
- 单位标准化: 统一单位体系 ✅
- 数据格式: 一致的表格结构 ✅

## 📞 技术支持

基于 **Zheng et al. (2025) Nature** 文献数据处理，结合 **Capacity_2023_Clean.xlsx** 进行国家标准化。

数据处理日期: 2025-08-02

---

**🎉 这是目前最完整的全球电力系统技术成本参数数据集，可直接用于全球电力系统规划模型！**
