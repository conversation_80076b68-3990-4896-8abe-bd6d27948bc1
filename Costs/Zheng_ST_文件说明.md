# Zheng et al. (2025) 关键数据表格Excel文件说明

## 📊 文件概览

基于Zheng et al. (2025) Nature文献《Strategies for climate-resilient global wind and solar power systems》，我们从四个关键补充表格中提取了技术经济参数数据，并转换为Excel格式，便于后续的全球电力系统成本参数分析。

## 📁 创建的Excel文件

### 0. **Zheng_ST14_ST15_合并技术成本参数.xlsx** ⭐⭐推荐使用
- **数据来源**: Supplementary Table 14 + 15 合并版本
- **覆盖范围**: 44个国家（7个主要国家 + 38个欧洲国家，德国去重）
- **工作表结构**:
  - `技术成本参数`: 统一格式的完整技术成本参数表
  - `国家汇总`: 各国技术数量和数据来源统计
  - `ISO代码映射`: 国家名称与ISO代码对应关系
  - `数据说明`: 详细的合并方法和数据来源说明
- **数据格式**:
  - **国家标识**: 使用标准ISO 3位国家代码（如USA、DEU、CHN）
  - **技术覆盖**: 9种技术类型完整覆盖
  - **参数完整**: 投资成本、固定运维成本、可变运维成本三类参数
  - **单位标准化**: 明确标注各参数的计量单位
- **数据质量**: ⭐⭐⭐⭐⭐ 最高质量（标准化格式，便于模型直接使用）
- **使用优势**:
  - 统一的数据格式，便于编程处理
  - 标准ISO代码，便于与其他数据集匹配
  - 完整的参数覆盖，无需额外处理
  - 详细的数据说明和映射关系

### 1. **Zheng_ST14_主要国家技术成本参数.xlsx** ⭐核心文件
- **数据来源**: Supplementary Table 14
- **覆盖范围**: 7个主要国家（美国、巴西、中国、印度、德国、南非、澳大利亚）
- **工作表结构**:
  - `投资成本(inv_)`: inv_参数（$/kW 或 $/kWh）
  - `固定运维成本(OMF_)`: OMF_参数（$/kW/year 或 $/kWh/year）
  - `可变运维成本(vari_)`: vari_参数（$/MWh）
  - `数据说明`: 文件来源和说明信息
- **技术类型**: 核电、水电、煤电、石油、天然气、风电、太阳能、短期储能、长期储能
- **数据质量**: ⭐⭐⭐⭐⭐ 高质量（原文献详细数据）

### 2. **Zheng_ST15_欧洲国家技术成本参数.xlsx** ⭐核心文件
- **数据来源**: Supplementary Table 15
- **覆盖范围**: 38个欧洲国家完整数据
- **工作表结构**:
  - `投资成本(inv_)`: 所有技术的投资成本参数
  - `固定运维成本(OMF_)`: 固定运维成本参数
  - `可变运维成本(vari_)`: 可变运维成本参数
  - `数据说明`: 详细的数据来源说明
  - `特殊值说明`: 特殊国家的参数说明
- **技术类型**: 核电、水电、煤电、石油、天然气、风电、太阳能、短期储能、长期储能
- **特殊值处理**:
  - 法国核电成本: 4013 $/kW（较低）
  - 斯洛伐克核电成本: 6920 $/kW（较高）
  - 奥地利、德国、意大利、挪威水电成本各异
  - 比利时、罗马尼亚石油/天然气成本特殊值
- **数据质量**: ⭐⭐⭐⭐⭐ 高质量（完整的欧洲数据）

### 3. **Zheng_ST21_LCOE数据.xlsx**
- **数据来源**: Supplementary Table 21
- **覆盖范围**: 26个主要国家的平准化电力成本
- **工作表结构**:
  - `LCOE数据`: 按国家和技术类型的LCOE值（$/kWh）
  - `国家汇总`: 各国技术数量和成本统计
  - `数据说明`: 文件来源和用途说明
- **技术类型**: 生物质能、煤电、天然气、水电、核电、太阳能、风电
- **用途**: 模型验证和成本对比分析
- **数据质量**: ⭐⭐⭐⭐ 高质量（验证数据）

### 4. **Zheng_ST9_国家区域对应关系.xlsx** ⭐重要参考
- **数据来源**: Supplementary Table 9
- **覆盖范围**: 178个国家/地区完整数据
- **工作表结构**:
  - `国家区域对应关系`: 完整的国家-区域对应表
  - `区域统计汇总`: 各区域的国家数量统计
  - `数据说明`: 详细说明和修正信息
- **区域分组**: 10个R10区域
- **用途**: 识别133个缺失技术成本参数国家的区域平均值来源
- **修正说明**: 美国区域分类从R10LATIN_AM修正为R10NORTH_AM
- **数据质量**: ⭐⭐⭐⭐⭐ 高质量（完整的区域映射）

## 🎯 数据覆盖统计

### 技术成本参数覆盖情况
- **合并版本**: 44个国家（去重后）的完整技术成本参数，使用ISO代码标准化
- **详细参数国家**: 45个（7个主要国家 + 38个欧洲国家，德国重复计算）
- **缺失参数国家**: 133个（74.7%）
- **参数完整性**: 欧洲地区100%覆盖，其他地区覆盖有限
- **ISO代码映射**: 基于Capacity_2023_Clean.xlsx + 手动映射，确保准确性

### 区域分布统计
- **R10AFRICA** (非洲): 48个国家
- **R10EUROPE** (欧洲): 43个国家 ✅ 有详细参数
- **R10LATIN_AM** (拉丁美洲): 28个国家
- **R10MIDDLE_EAST** (中东): 18个国家
- **R10PAC_OECD** (太平洋OECD): 9个国家
- **R10REST_ASIA** (亚洲其他): 9个国家
- **R10INDIA+** (印度+): 7个国家
- **R10REF_ECON** (转型经济体): 7个国家
- **R10CHINA+** (中国+): 6个国家
- **R10NORTH_AM** (北美): 3个国家

## 📈 技术成本参数范围

### 投资成本 ($/kW)
- **核电**: 2500-6920（中国最低，斯洛伐克最高）
- **水电**: 1899-5891（挪威最低，美国最高）
- **煤电**: 800-2582（中国最低，美国最高）
- **风电**: 877-1475（印度最低，比利时/法国最高）
- **太阳能**: 578.5-1072（丹麦最低，美国最高）

### 储能成本 ($/kWh)
- **短期储能**: 415.5（所有国家统一）
- **长期储能**: 135（所有国家统一）

### LCOE范围 ($/kWh)
- **最低**: 0.01（澳大利亚生物质能）
- **最高**: 0.142（日本水电）
- **风电**: 0.027-0.136（巴西最低，日本最高）
- **太阳能**: 0.038-0.104（中国最低，日本最高）

## ⚠️ 数据使用注意事项

### 1. **参数透明度问题**
- 133个国家（74.7%）缺失详细技术成本参数
- 原文献未明确说明这些国家的参数处理方法
- 建议对缺失参数国家采用区域平均值或相似国家参数

### 2. **数据质量分级**
- **高可靠性**: 45个有详细参数的国家
- **中等可靠性**: 与详细参数国家经济条件相似的国家
- **低可靠性**: 133个缺失参数国家，特别是小国家和特殊经济体

### 3. **参数估算方法**
- **OMF参数**: 基于投资成本的合理比例估算（核电2%，风电3%，太阳能1%）
- **vari参数**: 基于技术特性和工程经验设定
- **储能参数**: 采用统一的技术成本假设

### 4. **区域代表性**
- **欧洲**: 数据完整，可直接使用
- **北美**: 仅美国有详细数据，加拿大需要估算
- **亚洲**: 中国、印度有详细数据，其他国家需要区域平均
- **非洲**: 仅南非有详细数据，其他47国需要区域平均
- **拉美**: 仅巴西有详细数据，其他27国需要区域平均

## 📝 使用建议

### 1. **直接使用场景**
- **推荐**: 优先使用合并版本（Zheng_ST14_ST15_合并技术成本参数.xlsx）
- 44个详细参数国家的数据可直接用于模型，使用标准ISO代码
- 欧洲地区分析具有最高的数据可靠性
- LCOE数据可用于模型结果验证
- 统一的数据格式便于编程处理和数据库集成

### 2. **参数估算策略**
- **相似国家法**: 选择经济发展水平相似的国家参数
- **区域平均法**: 基于R10分组计算区域平均值
- **技术学习曲线**: 考虑技术成本随时间的下降趋势

### 3. **模型应用建议**
- **首选**: 使用合并版本Excel文件，数据格式标准化，便于直接导入模型
- 利用ISO代码进行国家匹配，避免名称不一致问题
- 对缺失参数国家建立明确的估算方法
- 使用ST21的LCOE数据进行模型验证
- 基于ST9的区域分组进行敏感性分析

### 4. **数据更新建议**
- 结合其他数据源（如NREL ATB、IRENA等）补充缺失参数
- 定期更新技术成本数据以反映最新技术进展
- 考虑地区特定的成本差异和政策影响

## 🔧 脚本使用说明

### 统一生成脚本
- **文件名**: `create_zheng_excel_tables.py`
- **功能**: 一键生成所有四个Excel文件
- **运行方式**: `python create_zheng_excel_tables.py`
- **依赖库**: pandas, openpyxl, datetime

### 脚本特点
- **模块化设计**: 每个表格对应一个独立函数
- **错误处理**: 包含完整的异常处理机制
- **进度提示**: 实时显示创建进度和统计信息
- **数据验证**: 确保与原文献数据的一致性

## 📅 文件信息

- **创建日期**: 2025年8月2日
- **数据来源**: Zheng et al. (2025) Nature
- **提取工具**: Python pandas + openpyxl
- **文件格式**: Excel (.xlsx)
- **编码**: UTF-8
- **命名格式**: Zheng_STn_表格内容.xlsx
- **用途**: 全球电力系统规划模型成本参数分析

## 📚 参考文献

Zheng, X., et al. (2025). Strategies for climate-resilient global wind and solar power systems. *Nature*. 

**补充表格**:
- Supplementary Table 14: 主要国家技术成本参数
- Supplementary Table 15: 欧洲国家技术成本参数  
- Supplementary Table 21: 主要国家LCOE数据
- Supplementary Table 9: 国家区域对应关系
