#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
第一步：标准化ST9国家区域映射文件
将所有国家名称替换为ISO代码，并扩展到202个国家
"""

import pandas as pd
from datetime import datetime

def load_country_iso_mapping():
    """加载国家名称到ISO代码的映射"""
    try:
        # 读取容量文件获取ISO代码映射
        df = pd.read_excel('../Capacity_2023/Capacity_2023_Clean.xlsx')
        country_mapping = dict(zip(df['Country'], df['ISO_Code']))
        
        # 手动映射一些特殊情况
        manual_mapping = {
            'U.S.': 'USA',
            'UK': 'GBR', 
            'South Africa': 'ZAF',
            'Czech Republic': 'CZE',
            'Bosnia and Herzegovina': 'BIH',
            'Macedonia': 'MKD',
            'North Macedonia': 'MKD',
            'United States': 'USA',
            'United Kingdom': 'GBR',
            'Russia': 'RUS',
            'Russian Federation': 'RUS',
            'Iran': 'IRN',
            'Venezuela': 'VEN',
            'Syria': 'SYR',
            'Tanzania': 'TZA',
            'Bolivia': 'BOL',
            'Moldova': 'MDA',
            'Laos': 'LAO',
            'Vietnam': 'VNM',
            'South Korea': 'KOR',
            'North Korea': 'PRK'
        }
        
        # 合并映射
        country_mapping.update(manual_mapping)
        return country_mapping, df
        
    except Exception as e:
        print(f"⚠️ 无法加载ISO映射文件: {e}")
        return {}, None

def get_countries_with_cost_data():
    """获取有技术成本数据的44个国家ISO代码"""
    try:
        df = pd.read_excel('Zheng_ST14_ST15_合并技术成本参数.xlsx', sheet_name='技术成本参数')
        countries_with_data = set(df['ISO_Code'].unique())
        return countries_with_data
    except Exception as e:
        print(f"⚠️ 无法读取成本数据文件: {e}")
        return set()

def standardize_st9_mapping():
    """标准化ST9国家区域映射文件"""
    
    print("🔄 第一步：标准化ST9国家区域映射文件...")
    
    # 加载映射数据
    country_mapping, capacity_df = load_country_iso_mapping()
    if capacity_df is None:
        print("❌ 无法加载必要的映射文件")
        return
    
    # 获取有成本数据的国家
    countries_with_data = get_countries_with_cost_data()
    print(f"✅ 识别到 {len(countries_with_data)} 个有技术成本数据的国家")
    
    # 读取原始ST9文件
    try:
        st9_df = pd.read_excel('Zheng_ST9_国家区域对应关系.xlsx', sheet_name='国家区域对应关系')
        print(f"✅ 读取原始ST9文件: {len(st9_df)} 个国家")
    except Exception as e:
        print(f"❌ 读取ST9文件失败: {e}")
        return
    
    # 创建国家名称到区域的映射
    country_to_region = {}
    for _, row in st9_df.iterrows():
        country_name = row['国家']
        region_code = row['区域代码']
        region_name = row['区域名称']
        country_to_region[country_name] = (region_code, region_name)
    
    # 为202个国家创建标准化映射
    standardized_data = []
    mapped_countries = set()
    unmapped_countries = []
    
    # 处理Capacity文件中的每个国家
    for _, row in capacity_df.iterrows():
        country_name = row['Country']
        iso_code = row['ISO_Code']
        
        # 查找区域映射
        region_code = None
        region_name = None
        
        # 直接匹配
        if country_name in country_to_region:
            region_code, region_name = country_to_region[country_name]
            mapped_countries.add(country_name)
        else:
            # 模糊匹配
            for st9_country in country_to_region.keys():
                if (country_name.lower() in st9_country.lower() or 
                    st9_country.lower() in country_name.lower() or
                    country_name.replace(' ', '').lower() == st9_country.replace(' ', '').lower()):
                    region_code, region_name = country_to_region[st9_country]
                    mapped_countries.add(st9_country)
                    break
        
        # 如果仍未找到，使用默认区域分配
        if region_code is None:
            unmapped_countries.append(country_name)
            # 基于地理位置的默认分配
            region_code, region_name = assign_default_region(country_name, iso_code)
        
        # 检查是否有技术成本数据
        has_cost_data = iso_code in countries_with_data
        
        standardized_data.append({
            'ISO_Code': iso_code,
            'Country': country_name,
            'Region_Code': region_code,
            'Region_Name': region_name,
            'Has_Cost_Data': has_cost_data,
            'Data_Source': 'ST14+ST15' if has_cost_data else 'To_Fill'
        })
    
    # 创建DataFrame并按ISO代码排序
    df_standardized = pd.DataFrame(standardized_data)
    df_standardized = df_standardized.sort_values('ISO_Code')
    
    print(f"\n📊 映射统计:")
    print(f"  成功映射: {len(mapped_countries)} 个ST9国家")
    print(f"  未映射国家: {len(unmapped_countries)} 个")
    print(f"  有成本数据: {len(countries_with_data)} 个国家")
    print(f"  需要填充: {len(df_standardized) - len(countries_with_data)} 个国家")
    
    if unmapped_countries:
        print(f"\n⚠️ 未映射的国家: {unmapped_countries[:10]}{'...' if len(unmapped_countries) > 10 else ''}")
    
    # 创建Excel文件
    with pd.ExcelWriter('Zheng_ST9_标准化国家区域映射.xlsx', engine='openpyxl') as writer:
        # 主数据工作表
        df_standardized.to_excel(writer, sheet_name='标准化国家区域映射', index=False)
        
        # 区域统计工作表
        region_stats = df_standardized.groupby(['Region_Code', 'Region_Name']).agg({
            'ISO_Code': 'count',
            'Has_Cost_Data': 'sum'
        }).reset_index()
        region_stats.columns = ['区域代码', '区域名称', '国家总数', '有数据国家数']
        region_stats['数据覆盖率'] = (region_stats['有数据国家数'] / region_stats['国家总数'] * 100).round(1)
        region_stats = region_stats.sort_values('国家总数', ascending=False)
        region_stats.to_excel(writer, sheet_name='区域统计', index=False)
        
        # 有数据国家列表
        countries_with_data_df = df_standardized[df_standardized['Has_Cost_Data'] == True][['ISO_Code', 'Country', 'Region_Code', 'Region_Name']]
        countries_with_data_df.to_excel(writer, sheet_name='有数据国家', index=False)
        
        # 数据说明工作表
        info_data = {
            '项目': ['数据来源', '处理日期', '国家总数', '有数据国家', '需填充国家', 
                   'ISO代码来源', '区域分类', '映射方法', '数据完整性'],
            '内容': ['Zheng et al. (2025) ST9 + Capacity_2023_Clean.xlsx', 
                   datetime.now().strftime('%Y-%m-%d'),
                   f'{len(df_standardized)}个国家',
                   f'{len(countries_with_data)}个国家（来自ST14+ST15）',
                   f'{len(df_standardized) - len(countries_with_data)}个国家',
                   'Capacity_2023_Clean.xlsx标准ISO 3位代码',
                   'R10区域分类系统（10个区域）',
                   '直接匹配 + 模糊匹配 + 地理默认分配',
                   '所有202个国家都有区域分配']
        }
        pd.DataFrame(info_data).to_excel(writer, sheet_name='数据说明', index=False)
    
    print(f"\n✅ 标准化ST9文件创建完成: Zheng_ST9_标准化国家区域映射.xlsx")
    print(f"📊 包含 {len(df_standardized)} 个国家的完整区域映射")
    
    return df_standardized

def assign_default_region(country_name, iso_code):
    """为未映射的国家分配默认区域"""

    # 基于ISO代码和地理知识的默认分配
    default_regions = {
        # 欧洲
        'AND': ('R10EUROPE', '欧洲'),  # 安道尔
        'LIE': ('R10EUROPE', '欧洲'),  # 列支敦士登
        'MCO': ('R10EUROPE', '欧洲'),  # 摩纳哥
        'SMR': ('R10EUROPE', '欧洲'),  # 圣马力诺
        'VAT': ('R10EUROPE', '欧洲'),  # 梵蒂冈
        'MLT': ('R10EUROPE', '欧洲'),  # 马耳他
        'GBR': ('R10EUROPE', '欧洲'),  # 英国
        'GRL': ('R10EUROPE', '欧洲'),  # 格陵兰（丹麦）

        # 北美
        'USA': ('R10NORTH_AM', '北美'),  # 美国
        'CAN': ('R10NORTH_AM', '北美'),  # 加拿大

        # 非洲
        'STP': ('R10AFRICA', '非洲'),   # 圣多美和普林西比
        'CPV': ('R10AFRICA', '非洲'),   # 佛得角
        'COM': ('R10AFRICA', '非洲'),   # 科摩罗
        'SYC': ('R10AFRICA', '非洲'),   # 塞舌尔
        'MUS': ('R10AFRICA', '非洲'),   # 毛里求斯
        'SWZ': ('R10AFRICA', '非洲'),   # 斯威士兰

        # 太平洋OECD
        'NZL': ('R10PAC_OECD', '太平洋OECD'),  # 新西兰
        'FJI': ('R10PAC_OECD', '太平洋OECD'),  # 斐济
        'PLW': ('R10PAC_OECD', '太平洋OECD'),  # 帕劳
        'TON': ('R10PAC_OECD', '太平洋OECD'),  # 汤加
        'VUT': ('R10PAC_OECD', '太平洋OECD'),  # 瓦努阿图
        'WSM': ('R10PAC_OECD', '太平洋OECD'),  # 萨摩亚
        'KIR': ('R10PAC_OECD', '太平洋OECD'),  # 基里巴斯
        'MHL': ('R10PAC_OECD', '太平洋OECD'),  # 马绍尔群岛
        'FSM': ('R10PAC_OECD', '太平洋OECD'),  # 密克罗尼西亚
        'NRU': ('R10PAC_OECD', '太平洋OECD'),  # 瑙鲁
        'TUV': ('R10PAC_OECD', '太平洋OECD'),  # 图瓦卢

        # 亚洲其他
        'MDV': ('R10REST_ASIA', '亚洲其他'),  # 马尔代夫
        'SGP': ('R10REST_ASIA', '亚洲其他'),  # 新加坡
        'BRN': ('R10REST_ASIA', '亚洲其他'),  # 文莱
        'HKG': ('R10REST_ASIA', '亚洲其他'),  # 香港
        'MAC': ('R10REST_ASIA', '亚洲其他'),  # 澳门
        'TWN': ('R10REST_ASIA', '亚洲其他'),  # 台湾
        'TLS': ('R10REST_ASIA', '亚洲其他'),  # 东帝汶
        'KOR': ('R10REST_ASIA', '亚洲其他'),  # 韩国

        # 拉丁美洲
        'ATG': ('R10LATIN_AM', '拉丁美洲'),   # 安提瓜和巴布达
        'BRB': ('R10LATIN_AM', '拉丁美洲'),   # 巴巴多斯
        'DMA': ('R10LATIN_AM', '拉丁美洲'),   # 多米尼克
        'GRD': ('R10LATIN_AM', '拉丁美洲'),   # 格林纳达
        'KNA': ('R10LATIN_AM', '拉丁美洲'),   # 圣基茨和尼维斯
        'LCA': ('R10LATIN_AM', '拉丁美洲'),   # 圣卢西亚
        'VCT': ('R10LATIN_AM', '拉丁美洲'),   # 圣文森特和格林纳丁斯
        'SUR': ('R10LATIN_AM', '拉丁美洲'),   # 苏里南

        # 中东
        'BHR': ('R10MIDDLE_EAST', '中东'),    # 巴林
        'QAT': ('R10MIDDLE_EAST', '中东'),    # 卡塔尔
        'KWT': ('R10MIDDLE_EAST', '中东'),    # 科威特
        'OMN': ('R10MIDDLE_EAST', '中东'),    # 阿曼
        'PSE': ('R10MIDDLE_EAST', '中东'),    # 巴勒斯坦
    }

    if iso_code in default_regions:
        return default_regions[iso_code]

    # 如果没有特定映射，使用通用分配
    print(f"⚠️ 使用默认区域分配: {country_name} ({iso_code}) -> 亚洲其他")
    return ('R10REST_ASIA', '亚洲其他')

if __name__ == "__main__":
    standardize_st9_mapping()
