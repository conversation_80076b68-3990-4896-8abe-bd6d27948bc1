#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终验证：全球202个国家技术成本参数数据集
"""

import pandas as pd

def final_validation():
    """最终验证数据集的完整性和正确性"""
    
    print("🔍 全球202个国家技术成本参数数据集 - 最终验证")
    print("=" * 60)
    
    try:
        # 检查Excel文件
        xl_file = pd.ExcelFile('Global_202_Countries_Technology_Cost_Parameters.xlsx')
        print(f"📋 工作表数量: {len(xl_file.sheet_names)}")
        print(f"📋 工作表列表: {xl_file.sheet_names}")
        
        # 检查主数据
        df_main = pd.read_excel('Global_202_Countries_Technology_Cost_Parameters.xlsx', sheet_name='全球技术成本参数')
        print(f"\n📊 主数据验证:")
        print(f"  总行数: {len(df_main)} (预期: 1818)")
        print(f"  国家数: {df_main['ISO_Code'].nunique()} (预期: 202)")
        print(f"  技术数: {df_main['Technology'].nunique()} (预期: 9)")
        print(f"  数据完整性: {'✅' if len(df_main) == 1818 else '❌'}")
        
        # 检查缺失值
        missing_values = df_main.isnull().sum().sum()
        print(f"  缺失值: {missing_values} {'✅' if missing_values == 0 else '❌'}")
        
        # 检查数据来源
        df_source = pd.read_excel('Global_202_Countries_Technology_Cost_Parameters.xlsx', sheet_name='数据来源')
        print(f"\n📈 数据来源验证:")
        print(f"  国家数: {len(df_source)} (预期: 202)")
        source_counts = df_source['数据来源类型'].value_counts()
        print(f"  原始数据: {source_counts.get('原始数据', 0)} 个国家")
        print(f"  区域平均: {source_counts.get('区域平均', 0)} 个国家")
        print(f"  全球平均: {source_counts.get('全球平均', 0)} 个国家")
        print(f"  总计: {source_counts.sum()} {'✅' if source_counts.sum() == 202 else '❌'}")
        
        # 检查成本参数合理性
        print(f"\n💰 成本参数验证:")
        cost_columns = ['Investment_Cost', 'Fixed_OM_Cost', 'Variable_OM_Cost']
        for col in cost_columns:
            min_val = df_main[col].min()
            max_val = df_main[col].max()
            negative_count = (df_main[col] < 0).sum()
            print(f"  {col}: [{min_val:.1f}, {max_val:.1f}], 负值: {negative_count} {'✅' if negative_count == 0 else '❌'}")
        
        # 检查关键国家
        print(f"\n🌍 关键国家验证:")
        key_countries = ['USA', 'CHN', 'DEU', 'JPN', 'IND', 'BRA', 'RUS', 'GBR', 'FRA', 'CAN']
        for iso in key_countries:
            country_data = df_main[df_main['ISO_Code'] == iso]
            source_data = df_source[df_source['ISO_Code'] == iso]
            if not country_data.empty and not source_data.empty:
                tech_count = len(country_data)
                data_type = source_data['数据来源类型'].iloc[0]
                quality = source_data['数据质量'].iloc[0]
                print(f"  {iso}: {tech_count}种技术, {data_type}, {quality}")
            else:
                print(f"  {iso}: ❌ 数据缺失")
        
        # 检查技术类型
        print(f"\n⚡ 技术类型验证:")
        expected_techs = ['nuclear', 'hydro', 'coal', 'oil', 'gas', 'wind', 'solar', 'stor1', 'stor2']
        actual_techs = df_main['Technology'].unique().tolist()
        print(f"  预期技术: {expected_techs}")
        print(f"  实际技术: {actual_techs}")
        print(f"  技术完整性: {'✅' if set(expected_techs) == set(actual_techs) else '❌'}")
        
        # 检查每个国家的技术完整性
        print(f"\n🔧 国家技术完整性验证:")
        incomplete_countries = []
        for iso in df_main['ISO_Code'].unique():
            country_techs = df_main[df_main['ISO_Code'] == iso]['Technology'].nunique()
            if country_techs != 9:
                incomplete_countries.append((iso, country_techs))
        
        if incomplete_countries:
            print(f"  ❌ 技术不完整的国家: {incomplete_countries}")
        else:
            print(f"  ✅ 所有202个国家都有完整的9种技术数据")
        
        # 检查区域统计
        df_region = pd.read_excel('Global_202_Countries_Technology_Cost_Parameters.xlsx', sheet_name='区域统计')
        print(f"\n🌏 区域统计验证:")
        print(f"  区域数量: {len(df_region)} (预期: 10)")
        total_countries_in_regions = df_region['总国家数'].sum()
        print(f"  区域内国家总数: {total_countries_in_regions} (预期: 202)")
        print(f"  区域完整性: {'✅' if total_countries_in_regions == 202 else '❌'}")
        
        # 最终评估
        print(f"\n🎯 最终评估:")
        all_checks = [
            len(df_main) == 1818,
            df_main['ISO_Code'].nunique() == 202,
            df_main['Technology'].nunique() == 9,
            missing_values == 0,
            len(df_source) == 202,
            source_counts.sum() == 202,
            all(df_main[col].min() >= 0 for col in cost_columns),
            len(incomplete_countries) == 0,
            total_countries_in_regions == 202
        ]
        
        passed_checks = sum(all_checks)
        total_checks = len(all_checks)
        
        print(f"  通过检查: {passed_checks}/{total_checks}")
        print(f"  数据质量: {'🎉 优秀' if passed_checks == total_checks else '⚠️ 需要检查'}")
        
        if passed_checks == total_checks:
            print(f"\n✅ 全球202个国家技术成本参数数据集验证通过！")
            print(f"📊 数据集已准备就绪，可用于全球电力系统规划模型")
        else:
            print(f"\n❌ 验证发现问题，请检查数据")
            
    except Exception as e:
        print(f"❌ 验证过程出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    final_validation()
