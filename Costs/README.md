# 全球电力系统成本参数数据库

## 📋 项目概览

本数据库为全球电力系统扩张规划模型提供完整的技术经济参数支持，基于最新的学术文献和标准化数据处理流程构建。

### 🎯 核心成果
- **全球202个国家**的完整技术成本参数覆盖
- **9种发电技术**的标准化经济参数
- **3类成本参数**：投资成本、固定运维成本、可变运维成本
- **多层次数据质量**：原始数据、区域平均、全球平均

---

## 📁 文件结构与用途说明

### 🎯 **核心数据集**

#### `Global_202_Countries_Technology_Cost_Parameters.xlsx` ⭐⭐⭐⭐⭐
**主要数据集** - 全球202个国家技术成本参数完整数据库
- **1818行数据**：202国家 × 9技术类型
- **6个工作表**：主数据、数据来源、区域统计、参数统计、填充方法说明、数据说明
- **用途**：直接用于全球电力系统规划模型的技术经济参数输入
- **数据质量**：100%完整性，无缺失值

#### `Global_202_Countries_数据集说明.md`
**数据集详细说明文档**
- 完整的数据集特点和质量分析
- 使用建议和模型应用指南
- 数据验证结果和可靠性评估

### 🔧 **数据处理脚本**

#### `fill_global_cost_parameters.py` ⭐⭐⭐⭐⭐
**主要数据处理脚本** - 实现完整的三步数据构建流程
- **功能**：区域代表性分析、参数填充策略、Excel文件生成
- **输入**：标准化映射文件 + 原始成本数据
- **输出**：完整的全球202国家数据集
- **依赖**：`Zheng_ST9_标准化国家区域映射.xlsx`, `Zheng_ST14_ST15_合并技术成本参数.xlsx`

#### `standardize_st9_mapping.py`
**国家区域映射标准化脚本**
- **功能**：将178国家扩展到202国家，ISO代码标准化
- **输入**：`Zheng_ST9_国家区域对应关系.xlsx`, `../Capacity_2023/Capacity_2023_Clean.xlsx`
- **输出**：`Zheng_ST9_标准化国家区域映射.xlsx`

#### `create_zheng_excel_tables.py`
**原始数据提取和整理脚本**
- **功能**：从文献数据中提取和合并技术成本参数
- **输出**：ST14、ST15、ST21等系列Excel文件

#### `final_validation.py`
**数据验证脚本**
- **功能**：全面验证数据完整性、合理性、一致性
- **验证项目**：9项关键检查，确保数据质量

### 📊 **原始数据文件**

#### Zheng et al. (2025) 系列数据
- `Zheng_ST14_主要国家技术成本参数.xlsx` - 主要国家原始数据
- `Zheng_ST15_欧洲国家技术成本参数.xlsx` - 欧洲国家原始数据
- `Zheng_ST14_ST15_合并技术成本参数.xlsx` - 44个国家合并数据（⭐⭐⭐⭐⭐质量）
- `Zheng_ST21_LCOE数据.xlsx` - 平准化电力成本数据
- `Zheng_ST9_国家区域对应关系.xlsx` - 原始区域映射
- `Zheng_ST9_标准化国家区域映射.xlsx` - 标准化区域映射
- `Zheng_ST_文件说明.md` - 原始数据说明文档

#### 参考数据
- `ATB_NREL_2024_v3_Workbook.xlsx` - NREL年度技术基准数据
- `Projected-Costs-of-Generating-Electricity-2020.pdf` - IEA/NEA发电成本预测
- `costs_2023.xlsx` - 2023年成本数据参考
- `储能技术经济参数.png` - 储能技术参数图表

---

## 🔄 数据构建流程

### 第一步：标准化国家区域映射
```bash
python standardize_st9_mapping.py
```
- **输入**：178个国家的原始区域映射
- **处理**：扩展到202个国家，ISO代码标准化，区域分配
- **输出**：标准化的国家区域映射文件

### 第二步：参数填充策略实施
```bash
python fill_global_cost_parameters.py
```
- **区域代表性分析**：识别7个有代表国家的区域，3个无代表区域
- **智能填充策略**：
  - 有代表区域 → 区域平均值填充
  - 无代表区域 → 全球平均值填充
- **数据验证**：完整性和合理性检查

### 第三步：生成完整数据集
- **主数据表**：1818行完整技术成本参数
- **辅助表格**：数据来源、区域统计、参数统计等
- **质量控制**：多层次验证和文档说明

### 验证流程
```bash
python final_validation.py
```
- **9项关键检查**：数据完整性、参数合理性、国家覆盖等
- **质量评级**：⭐⭐⭐⭐⭐（优秀）

---

## 📊 数据获取方法

### 🔬 **数据来源**
**主要来源**：Zheng et al. (2025) "Strategies for climate-resilient global wind and solar power systems" *Nature*
- **原始数据覆盖**：44个国家的详细技术成本参数
- **数据类型**：投资成本、固定运维成本、可变运维成本
- **技术范围**：9种发电和储能技术

**辅助来源**：
- **NREL ATB 2024**：美国国家可再生能源实验室年度技术基准
- **IEA/NEA 2020**：国际能源署发电成本预测报告
- **Capacity_2023_Clean.xlsx**：全球电力装机容量数据（国家标准化参考）

### 🌍 **三种数据填充策略**

#### 1. 原始数据（44个国家，21.8%）⭐⭐⭐⭐⭐
- **来源**：直接来自Zheng et al. (2025) ST14+ST15表格
- **质量**：最高可靠性，文献原始数据
- **国家示例**：USA, CHN, DEU, GBR, FRA, IND, BRA等

#### 2. 区域平均值（117个国家，57.9%）⭐⭐⭐⭐
- **策略**：使用同区域内有原始数据国家的平均值
- **适用区域**：7个有代表国家的R10区域
- **质量**：较高可靠性，基于相同区域实际数据

#### 3. 全球平均值（41个国家，20.3%）⭐⭐⭐
- **策略**：使用全球44个有数据国家的平均值
- **适用区域**：3个无代表国家的R10区域（中东、转型经济体、亚洲其他）
- **质量**：中等可靠性，基于全球平均水平

### 🗺️ **R10区域分类系统**
标准化的10区域分类，确保全球覆盖：
- **R10AFRICA** (非洲): 49个国家
- **R10EUROPE** (欧洲): 49个国家  
- **R10LATIN_AM** (拉丁美洲): 33个国家
- **R10MIDDLE_EAST** (中东): 19个国家
- **R10PAC_OECD** (太平洋OECD): 14个国家
- **R10REST_ASIA** (亚洲其他): 15个国家
- **R10INDIA+** (印度+): 7个国家
- **R10REF_ECON** (转型经济体): 7个国家
- **R10CHINA+** (中国+): 6个国家
- **R10NORTH_AM** (北美): 3个国家

### 🏷️ **ISO国家代码标准化**
- **标准**：ISO 3166-1 alpha-3 三位国家代码
- **覆盖**：202个国家完整覆盖
- **参考**：基于Capacity_2023_Clean.xlsx权威映射

---

## 🔋 当前数据集覆盖范围

### ⚡ **技术类型覆盖（9种）**

#### 传统发电技术
- **nuclear** (核电) - 核裂变发电技术
- **hydro** (水电) - 水力发电技术  
- **coal** (煤电) - 燃煤发电技术
- **oil** (石油) - 燃油发电技术
- **gas** (天然气) - 燃气发电技术

#### 可再生能源技术
- **wind** (风电) - 风力发电技术
- **solar** (太阳能) - 太阳能光伏发电技术

#### 储能技术
- **stor1** (短期储能) - 短期电力储存技术
- **stor2** (长期储能) - 长期电力储存技术

### 💰 **成本参数类型（3类）**

#### 投资成本 (Investment Cost)
- **单位**：$/kW (发电技术), $/kWh (储能技术)
- **范围**：135.0 - 6920.0
- **说明**：设备采购和安装的一次性投资

#### 固定运维成本 (Fixed O&M Cost)  
- **单位**：$/kW/year (发电技术), $/kWh/year (储能技术)
- **范围**：2.7 - 138.4
- **说明**：与装机容量相关的年度固定维护费用

#### 可变运维成本 (Variable O&M Cost)
- **单位**：$/MWh
- **范围**：0.0 - 67.0  
- **说明**：与发电量相关的变动运维费用

### 🌍 **地理覆盖范围**
- **总覆盖**：202个国家/地区
- **完整性**：100%全球覆盖，无遗漏
- **数据质量分布**：
  - 高质量原始数据：44个国家
  - 中高质量区域平均：117个国家  
  - 中等质量全球平均：41个国家

---

## 🚀 未来扩展计划

### 🌱 **生物质能经济参数扩展**
*（预留章节 - 待实施）*

#### 计划添加技术类型
- **biomass_solid** - 固体生物质发电
- **biomass_gas** - 生物质气化发电  
- **biomass_liquid** - 液体生物燃料发电
- **biogas** - 沼气发电

#### 数据获取策略
- 文献调研：最新生物质能技术成本研究
- 区域差异化：考虑生物质资源可获得性
- 技术成熟度：区分商业化和示范阶段技术

#### 预期数据结构
```
生物质能技术成本参数表
├── 国家代码 (ISO_Code)
├── 技术类型 (biomass_*)  
├── 投资成本 ($/kW)
├── 固定运维成本 ($/kW/year)
├── 可变运维成本 ($/MWh)
└── 燃料成本 ($/MWh) [新增]
```

### ⚙️ **机组技术约束参数扩展**
*（预留章节 - 待实施）*

#### 计划添加约束类型
- **启停时间约束**
  - 最小开机时间 (hours)
  - 最小停机时间 (hours)
  - 启动成本 ($/start)
  
- **爬坡约束**  
  - 向上爬坡率 (%/min)
  - 向下爬坡率 (%/min)
  - 最小技术出力 (% of capacity)
  
- **运行约束**
  - 年度可用率 (%)
  - 计划维护周期 (days/year)
  - 强迫停运率 (%)

#### 数据获取策略
- 技术手册：设备制造商技术规格
- 运行经验：电网公司实际运行数据
- 标准规范：电力系统规划设计标准

#### 预期数据结构
```
机组技术约束参数表
├── 国家代码 (ISO_Code)
├── 技术类型 (Technology)
├── 最小开机时间 (hours)
├── 最小停机时间 (hours)  
├── 启动成本 ($/start)
├── 向上爬坡率 (%/min)
├── 向下爬坡率 (%/min)
├── 最小技术出力 (%)
├── 年度可用率 (%)
└── 强迫停运率 (%)
```

### 📈 **数据更新维护计划**
- **年度更新**：跟踪技术成本变化趋势
- **文献跟踪**：持续监控最新研究成果
- **质量改进**：逐步提高数据覆盖和精度
- **方法优化**：改进填充策略和验证方法

---

## 📖 使用指南

### 🎯 **快速开始**
1. **主数据获取**：直接使用 `Global_202_Countries_Technology_Cost_Parameters.xlsx`
2. **数据验证**：运行 `python final_validation.py` 确认数据完整性
3. **模型集成**：参考"数据说明"工作表了解数据结构和使用建议

### 💡 **最佳实践**
- **优先级使用**：原始数据 > 区域平均 > 全球平均
- **敏感性分析**：基于数据质量等级进行不确定性分析
- **参数验证**：使用参数统计表检查数据合理性
- **定期更新**：建议年度更新以反映技术进步

### ⚠️ **注意事项**
- 储能技术成本单位为$/kWh，发电技术为$/kW
- 全球平均数据适用于缺乏区域代表的情况，使用时需谨慎
- 数据基于2025年文献，实际应用时需考虑时间因素

---

## 📞 技术支持

### 📚 **相关文献**
- Zheng et al. (2025) "Strategies for climate-resilient global wind and solar power systems" *Nature*
- NREL Annual Technology Baseline 2024
- IEA/NEA Projected Costs of Generating Electricity 2020

### 🔧 **维护信息**
- **创建日期**：2025-08-02
- **最后更新**：2025-08-02  
- **数据版本**：v1.0
- **验证状态**：✅ 9/9项检查通过

---

*本数据库为全球电力系统扩张规划模型提供标准化、高质量的技术经济参数支持。*
